{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 16 2019", "platform": "x64"}, "paths": {"cmake": "D:/C++/bin/cmake.exe", "cpack": "D:/C++/bin/cpack.exe", "ctest": "D:/C++/bin/ctest.exe", "root": "D:/C++/share/cmake-3.23"}, "version": {"isDirty": false, "major": 3, "minor": 23, "patch": 0, "string": "3.23.0", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-72a5777f3037fe49dbea.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "cache-v2-72f3e7b281a5c38c990f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-433d4053d25fa902fbe4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-612639907d3c82286202.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-72f3e7b281a5c38c990f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-72a5777f3037fe49dbea.json", "kind": "codemodel", "version": {"major": 2, "minor": 4}}, {"jsonFile": "toolchains-v1-612639907d3c82286202.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-433d4053d25fa902fbe4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}