# -*- coding: utf-8 -*-
# 文件名: yolo_model_prober.py
# 功能: YOLO模型输出格式自动探测模块

import numpy as np
import onnxruntime as ort
import logging
from typing import Dict, Any, Tuple, List, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("YOLOModelProber")

class YOLOModelProber:
    """
    YOLO模型输出格式探测器
    
    该类负责分析YOLO模型的输出张量格式，确定:
    1. 输出是否需要转置
    2. 输出的维度结构
    3. 输出中各元素的含义
    4. 适合该模型的后处理策略
    """
    
    def __init__(self, verbose: bool = True):
        """
        初始化YOLO模型探测器
        
        Args:
            verbose: 是否输出详细日志
        """
        self.verbose = verbose
        self.model_info = {}
        self.has_probed = False
    
    def probe(self, model_path: str) -> Dict[str, Any]:
        """
        探测指定模型的输出格式特征
        
        Args:
            model_path: ONNX模型文件的路径
            
        Returns:
            包含模型特征的字典
        """
        if self.verbose:
            logger.info(f"开始探测模型: {model_path}")
            
        # 1. 创建一个推理会话来获取模型信息
        try:
            session = ort.InferenceSession(model_path)
            
            # 2. 获取模型输入信息
            input_details = session.get_inputs()[0]
            input_name = input_details.name
            input_shape = input_details.shape
            
            # 3. 获取模型输出信息
            output_details = session.get_outputs()[0]
            output_name = output_details.name
            output_shape = output_details.shape
            
            if self.verbose:
                logger.info(f"模型输入: {input_name}, 形状: {input_shape}")
                logger.info(f"模型输出: {output_name}, 形状: {output_shape}")
            
            # 4. 分析输出形状以确定格式
            model_type, format_info = self._analyze_output_shape(output_shape)
            
            # 5. 组合所有信息
            self.model_info = {
                "model_path": model_path,
                "input_name": input_name,
                "input_shape": input_shape,
                "output_name": output_name,
                "output_shape": output_shape,
                "model_type": model_type,
                "format_info": format_info,
            }
            
            self.has_probed = True
            return self.model_info
            
        except Exception as e:
            logger.error(f"模型探测失败: {str(e)}")
            raise
    
    def _analyze_output_shape(self, output_shape: List[int]) -> Tuple[str, Dict[str, Any]]:
        """
        分析输出张量形状，确定模型类型和格式信息
        
        Args:
            output_shape: 输出张量的形状
            
        Returns:
            (模型类型, 格式信息)
        """
        # 去掉批次维度
        if output_shape[0] == 1:
            dims = output_shape[1:]
        else:
            dims = output_shape
        
        if len(dims) != 2:
            if self.verbose:
                logger.warning(f"意外的输出维度: {output_shape}, 预期为2维或3维")
            return "unknown", {"needs_transpose": False}
        
        format_info = {}
        
        # 假设YOLOv5/v8的典型输出形状:
        # [1, num_classes + 5, num_boxes] 或 [1, num_boxes, num_classes + 5]
        # 其中每个框包含: [x, y, w, h, confidence, class_scores...]
        
        # 确定是否需要转置
        # 如果第二维远小于第三维，通常意味着需要转置
        if dims[0] < dims[1] and dims[0] < 85:  # 通常YOLO维度 < 85 (COCO有80类)
            format_info["needs_transpose"] = True
            # 猜测: 维度0是框的属性数，维度1是框的数量
            num_attributes = dims[0]
            num_boxes = dims[1]
            
            if self.verbose:
                logger.info(f"检测到需要转置: 当前形状 {dims} -> 目标形状 [{num_boxes}, {num_attributes}]")
        else:
            format_info["needs_transpose"] = False
            # 猜测: 维度0是框的数量，维度1是框的属性数
            num_boxes = dims[0]
            num_attributes = dims[1]
            
            if self.verbose:
                logger.info(f"无需转置: 当前形状 {dims}")
        
        # 增强版: 更精确地确定模型类型、类别数量和特征
        # YOLOv5: 通常是 5+num_classes (x,y,w,h,obj,classes...)
        # YOLOv8: 通常是 4+num_classes (x,y,w,h,classes...) - 没有单独的objectness
        
        # 估计类别数量
        if num_attributes == 85:
            # 标准COCO模型: 80类 + 5
            num_classes = 80
            model_type = "yolov5"  # 假设是v5，因为v5更常见
            has_obj_score = True
        elif num_attributes == 84:
            # 可能是YOLOv8 COCO: 80类 + 4 (没有单独的objectness)
            num_classes = 80
            model_type = "yolov8"
            has_obj_score = False
        else:
            # 自定义类别数量
            # 检查是否可能是YOLOv8 (通常没有objectness分数，所以特征数=4+类别数)
            # 或者是YOLOv5 (通常有objectness分数，所以特征数=5+类别数)
            if num_attributes >= 6:  # 至少需要坐标 + 分数 + 至少1个类别
                # 尝试推断模型类型和类别数
                # 我们试两种假设，看哪种更合理：YOLOv5 或 YOLOv8
                
                # 假设1: 是YOLOv5 (有objectness分数)
                v5_num_classes = num_attributes - 5
                # 假设2: 是YOLOv8 (没有objectness分数)
                v8_num_classes = num_attributes - 4
                
                # YOLOv8更新，比YOLOv5更可能缺少objectness分数
                if v8_num_classes > 0 and (v8_num_classes % 1 == 0):
                    model_type = "yolov8"
                    num_classes = v8_num_classes
                    has_obj_score = False
                    if self.verbose:
                        logger.info(f"推测为YOLOv8模型，类别数量: {num_classes}")
                else:
                    model_type = "yolov5"
                    num_classes = v5_num_classes
                    has_obj_score = True
                    if self.verbose:
                        logger.info(f"推测为YOLOv5模型，类别数量: {num_classes}")
            else:
                # 无法确定类别数，可能是未知格式
                if num_attributes >= 5:  # 假设至少是xywh+1
                    num_classes = 1
                    model_type = "unknown"
                    has_obj_score = False
                else:
                    # 实在无法解释的格式
                    num_classes = 1
                    model_type = "unknown"
                    has_obj_score = False
                    
                if self.verbose:
                    logger.warning(f"无法确定模型格式，将假设类别数量为{num_classes}")

        # 对检测到的类别数量进行合理性检查
        if num_classes <= 0:
            if self.verbose:
                logger.warning(f"检测到的类别数 {num_classes} 无效，将其设为1")
            num_classes = 1
        elif num_classes > 1000:
            # 可能是格式错误或者不是YOLO模型
            if self.verbose:
                logger.warning(f"检测到的类别数 {num_classes} 异常大，可能不是YOLO模型")
            num_classes = 1
        
        # 根据估计的模型类型确定输出格式
        format_info.update({
            "num_boxes": num_boxes,
            "num_attributes": num_attributes,
            "num_classes": num_classes,
            "has_obj_score": has_obj_score,
            "box_format": "xywh",  # YOLO通常使用中心点+宽高
            "coordinate_format": "relative"  # YOLO通常使用相对坐标(0-1)
        })
        
        if self.verbose:
            logger.info(f"估计模型类型: {model_type}")
            logger.info(f"估计格式信息: {format_info}")
        
        return model_type, format_info
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取探测结果"""
        if not self.has_probed:
            raise RuntimeError("请先调用probe()方法探测模型")
        return self.model_info
    
    def get_recommended_processor_settings(self) -> Dict[str, Any]:
        """
        获取推荐的后处理器设置
        
        Returns:
            包含后处理器设置的字典
        """
        if not self.has_probed:
            raise RuntimeError("请先调用probe()方法探测模型")
            
        model_type = self.model_info["model_type"]
        format_info = self.model_info["format_info"]
        
        settings = {
            "needs_transpose": format_info["needs_transpose"],
            "box_format": "xywh",  # YOLO通常使用xywh格式
            "has_obj_confidence": format_info["has_obj_score"],
            "class_offset": 5 if format_info["has_obj_score"] else 4,
            # 其他设置
            "confidence_threshold": 0.5,  # 默认置信度阈值
            "nms_threshold": 0.4,        # 默认NMS阈值
        }
        
        # 根据不同模型类型调整设置
        if model_type == "yolov8":
            # YOLOv8特定设置
            settings["confidence_threshold"] = 0.25  # v8通常需要更低的阈值
            settings["nms_threshold"] = 0.7         # v8通常使用更高的NMS阈值
            
        elif model_type == "yolov5":
            # YOLOv5特定设置
            settings["confidence_threshold"] = 0.5
            settings["nms_threshold"] = 0.5
            
        return settings

# 测试代码
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = "models/1.onnx"  # 默认测试模型
        
    prober = YOLOModelProber(verbose=True)
    model_info = prober.probe(model_path)
    
    print("\n===== 模型探测结果 =====")
    for key, value in model_info.items():
        print(f"{key}: {value}")
    
    print("\n===== 推荐后处理设置 =====")
    settings = prober.get_recommended_processor_settings()
    for key, value in settings.items():
        print(f"{key}: {value}") 