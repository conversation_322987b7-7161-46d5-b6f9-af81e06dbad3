# Governance

The governance model for the JSON for Modern C++ project is a **Benevolent Dictator for Life (BDFL)** structure. As the
sole maintainer, [<PERSON><PERSON>](https://github.com/n<PERSON><PERSON>) is responsible for all key aspects of the project. The
project governance may evolve as the project grows, but any changes will be documented here and communicated to
contributors.

## Overview

This project is led by a benevolent dictator, [<PERSON><PERSON>](https://github.com/n<PERSON>hmann), and managed by the
community. That is, the community actively contributes to the day-to-day maintenance of the project, but the general
strategic line is drawn by the benevolent dictator. In case of disagreement, they have the last word. It is the
benevolent dictator’s job to resolve disputes within the community and to ensure that the project is able to progress in
a coordinated way. In turn, it is the community’s job to guide the decisions of the benevolent dictator through active
engagement and contribution.

## Roles and responsibilities

### Benevolent dictator (project lead)

Typically, the benevolent dictator, or project lead, is self-appointed. However, because the community always has the
ability to fork, this person is fully answerable to the community. The project lead’s role is a difficult one: they set
the strategic objectives of the project and communicate these clearly to the community. They also have to understand the
community as a whole and strive to satisfy as many conflicting needs as possible, while ensuring that the project
survives in the long term.

In many ways, the role of the benevolent dictator is less about dictatorship and more about diplomacy. The key is to
ensure that, as the project expands, the right people are given influence over it and the community rallies behind the
vision of the project lead. The lead’s job is then to ensure that the committers (see below) make the right decisions on
behalf of the project. Generally speaking, as long as the committers are aligned with the project’s strategy, the
project lead will allow them to proceed as they desire.

### Committers

Committers are contributors who have made several valuable contributions to the project and are now relied upon to both
write code directly to the repository and screen the contributions of others. In many cases they are programmers but it
is also possible that they contribute in a different role. Typically, a committer will focus on a specific aspect of the
project, and will bring a level of expertise and understanding that earns them the respect of the community and the
project lead. The role of committer is not an official one, it is simply a position that influential members of the
community will find themselves in as the project lead looks to them for guidance and support.

Committers have no authority over the overall direction of the project. However, they do have the ear of the project
lead. It is a committer’s job to ensure that the lead is aware of the community’s needs and collective objectives, and
to help develop or elicit appropriate contributions to the project. Often, committers are given informal control over
their specific areas of responsibility, and are assigned rights to directly modify certain areas of the source code.
That is, although committers do not have explicit decision-making authority, they will often find that their actions are
synonymous with the decisions made by the lead.

### Contributors

Contributors are community members who either have no desire to become committers, or have not yet been given the
opportunity by the benevolent dictator. They make valuable contributions, such as those outlined in the list below, but
generally do not have the authority to make direct changes to the project code. Contributors engage with the project
through communication tools, such as email lists, and via reports and patches attached to issues in the issue tracker,
as detailed in our community tools document.

Anyone can become a contributor. There is no expectation of commitment to the project, no specific skill requirements
and no selection process. To become a contributor, a community member simply has to perform one or more actions that are
beneficial to the project.

Some contributors will already be engaging with the project as users, but will also find themselves doing one or more of
the following:

- supporting new users (current users often provide the most effective new user support)
- reporting bugs
- identifying requirements
- supplying graphics and web design
- programming
- assisting with project infrastructure
- writing documentation
- fixing bugs
- adding features

As contributors gain experience and familiarity with the project, they may find that the project lead starts relying on
them more and more. When this begins to happen, they gradually adopt the role of committer, as described above.

### Users

Users are community members who have a need for the project. They are the most important members of the community:
without them, the project would have no purpose. Anyone can be a user; there are no specific requirements.

Users should be encouraged to participate in the life of the project and the community as much as possible. User
contributions enable the project team to ensure that they are satisfying the needs of those users. Common user
activities include (but are not limited to):

- evangelising about the project
- informing developers of project strengths and weaknesses from a new user’s perspective
- providing moral support (a ‘thank you’ goes a long way)
- providing financial support

Users who continue to engage with the project and its community will often find themselves becoming more and more
involved. Such users may then go on to become contributors, as described above.

## Support

All participants in the community are encouraged to provide support for new users within the project management
infrastructure. This support is provided as a way of growing the community. Those seeking support should recognise that
all support activity within the project is voluntary and is therefore provided as and when time allows. A user requiring
guaranteed response times or results should therefore seek to purchase a support contract from a vendor. (Of course,
that vendor should be an active member of the community.) However, for those willing to engage with the project on its
own terms, and willing to help support other users, the community support channels are ideal.

## Contribution Process

Anyone can contribute to the project, regardless of their skills, as there are many ways to contribute. For instance, a
contributor might be active on the project mailing list and issue tracker, or might supply patches. The various ways of
contributing are described in more detail in our roles in open source document.

The developer mailing list is the most appropriate place for a contributor to ask for help when making their first
contribution.

## Decision-Making Process

The benevolent dictatorship model does not need a formal conflict resolution process, since the project lead’s word is
final. If the community chooses to question the wisdom of the actions of a committer, the project lead can review their
decisions by checking the email archives, and either uphold or reverse them.

---

!!! quote "Source"

    The text was taken from http://oss-watch.ac.uk/resources/benevolentdictatorgovernancemodel.
