#pragma once

#include <string>

// KMBox硬件鼠标控制器配置
struct KMBoxConfig {
    std::string ip = "*************";        // KMBox IP地址
    std::string port = "8808";               // KMBox端口
    std::string mac = "62587019";                // KMBox MAC地址
    bool use_smooth_move = true;             // 是否使用平滑移动
    int smooth_move_time_ms = 50;            // 平滑移动时间(毫秒)
    bool use_encryption = true;              // 是否使用加密传输
    bool enabled = false;                    // 是否启用KMBox控制
};

// KMBox硬件鼠标控制器
class KMBoxController {
private:
    KMBoxConfig config_;
    bool initialized_;
    bool connected_;

public:
    KMBoxController(const KMBoxConfig& config = KMBoxConfig{});
    ~KMBoxController();

    // 初始化并连接KMBox
    bool Initialize();

    // 断开连接
    void Disconnect();

    // 检查是否已连接
    bool IsConnected() const { return connected_; }

    // 检查是否启用
    bool IsEnabled() const { return config_.enabled; }

    // 启用/禁用KMBox控制
    void SetEnabled(bool enabled) { config_.enabled = enabled; }

    // 更新配置
    void UpdateConfig(const KMBoxConfig& config);

    // 获取配置
    KMBoxConfig GetConfig() const { return config_; }

    // 鼠标移动控制
    bool MoveMouse(int dx, int dy);                    // 相对移动
    bool MoveMouseSmooth(int dx, int dy, int time_ms); // 平滑移动
    bool MoveMouseAuto(int dx, int dy);                // 自动平滑移动(使用配置的时间)

    // 鼠标按键控制
    bool LeftClick();
    bool LeftDown();
    bool LeftUp();
    bool RightClick();
    bool RightDown();
    bool RightUp();

    // 监控功能
    bool IsRightButtonPressed();  // 检查右键是否按下
    bool IsLeftButtonPressed();   // 检查左键是否按下

    // 测试连接
    bool TestConnection();

private:
    // 内部辅助函数
    bool InitializeKMBox();
    void LogError(const std::string& message, int error_code = 0);
    void LogInfo(const std::string& message);
};