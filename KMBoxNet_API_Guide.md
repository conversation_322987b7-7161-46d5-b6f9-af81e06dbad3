# KMBoxNet API 使用指南

## 概述

KMBoxNet是一个硬件级的网络鼠标键盘模拟器，通过网络连接实现远程控制。本文档详细说明如何在C++程序中集成和使用KMBoxNet API。

## 快速开始

### 1. 基本设置

```cpp
#include "NetConfig/kmboxNet.h"
#include "NetConfig/HidTable.h"
#include <iostream>

// 连接KMBox设备
int ret = kmNet_init("*************", "8808", "62587019");
if (ret != 0) {
    std::cout << "连接失败，错误码: " << ret << std::endl;
    return -1;
}
std::cout << "连接成功！" << std::endl;
```

### 2. 基本鼠标控制

```cpp
// 鼠标移动（相对移动）
kmNet_mouse_move(100, 50);  // 向右移动100像素，向下移动50像素

// 鼠标点击
kmNet_mouse_left(1);   // 按下左键
Sleep(100);            // 等待100毫秒
kmNet_mouse_left(0);   // 松开左键

// 鼠标滚轮
kmNet_mouse_wheel(3);  // 向上滚动
kmNet_mouse_wheel(-3); // 向下滚动
```

## 核心API参考

### 连接管理

#### `kmNet_init(char* ip, char* port, char* mac)`
**功能**: 连接KMBox设备  
**参数**:
- `ip`: 设备IP地址（如"*************"）
- `port`: 端口号（如"8808"）  
- `mac`: MAC地址（如"62587019"）
**返回值**: 0=成功，其他值=错误码

### 鼠标控制API

#### 基础移动
```cpp
int kmNet_mouse_move(short x, short y);
```
- **功能**: 相对移动鼠标
- **参数**: x,y为相对移动距离（像素）
- **示例**: `kmNet_mouse_move(100, -50);` // 右移100，上移50

#### 鼠标按键
```cpp
int kmNet_mouse_left(int isdown);    // 左键：1=按下，0=松开
int kmNet_mouse_right(int isdown);   // 右键
int kmNet_mouse_middle(int isdown);  // 中键
int kmNet_mouse_side1(int isdown);   // 侧键1
int kmNet_mouse_side2(int isdown);   // 侧键2
```

#### 鼠标滚轮
```cpp
int kmNet_mouse_wheel(int wheel);
```
- **参数**: 正值=向上滚动，负值=向下滚动
- **示例**: `kmNet_mouse_wheel(3);` // 向上滚动3格

#### 组合操作
```cpp
int kmNet_mouse_all(int button, int x, int y, int wheel);
```
- **功能**: 一次调用完成多个操作
- **参数**: 
  - `button`: 按键状态（1=左键，2=右键，4=中键）
  - `x,y`: 移动距离
  - `wheel`: 滚轮值

### 高级移动API

#### 自动轨迹移动
```cpp
int kmNet_mouse_move_auto(int x, int y, int time_ms);
```
- **功能**: 平滑移动到目标位置
- **参数**: 
  - `x,y`: 目标相对位置
  - `time_ms`: 移动耗时（毫秒）
- **示例**: `kmNet_mouse_move_auto(200, 100, 1000);` // 1秒内移动到(200,100)

#### 贝塞尔曲线移动
```cpp
int kmNet_mouse_move_beizer(int x, int y, int ms, int x1, int y1, int x2, int y2);
```
- **功能**: 使用贝塞尔曲线实现自然轨迹
- **参数**:
  - `x,y`: 终点坐标
  - `ms`: 移动时间
  - `x1,y1`: 控制点1
  - `x2,y2`: 控制点2

### 监听功能

#### 启动监听
```cpp
int kmNet_monitor(short port);  // 启动监听，端口通常为1000
```

#### 查询鼠标状态
```cpp
int kmNet_monitor_mouse_left();     // 返回1=按下，0=未按下
int kmNet_monitor_mouse_right();    
int kmNet_monitor_mouse_middle();   
int kmNet_monitor_mouse_side1();    
int kmNet_monitor_mouse_side2();    
int kmNet_monitor_mouse_xy(int* x, int* y);      // 获取鼠标位置
int kmNet_monitor_mouse_wheel(int* wheel);       // 获取滚轮状态
```

### 加密API（推荐）

所有基础API都有对应的加密版本，函数名前缀为`kmNet_enc_`：

```cpp
kmNet_enc_mouse_move(100, 50);      // 加密版本的鼠标移动
kmNet_enc_mouse_left(1);            // 加密版本的左键点击
kmNet_enc_mouse_move_auto(200, 100, 1000);  // 加密版本的自动移动
```

## 实用代码示例

### 示例1: 简单点击
```cpp
void simple_click(int x, int y) {
    kmNet_mouse_move(x, y);          // 移动到目标位置
    Sleep(50);                       // 短暂延迟
    kmNet_mouse_left(1);             // 按下左键
    Sleep(100);                      // 保持按下
    kmNet_mouse_left(0);             // 松开左键
}
```

### 示例2: 拖拽操作
```cpp
void drag_mouse(int start_x, int start_y, int end_x, int end_y) {
    kmNet_mouse_move(start_x, start_y);      // 移动到起始位置
    Sleep(100);
    kmNet_mouse_left(1);                     // 按下左键
    Sleep(50);
    kmNet_mouse_move_auto(end_x - start_x, end_y - start_y, 500);  // 拖拽到目标位置
    Sleep(100);
    kmNet_mouse_left(0);                     // 松开左键
}
```

### 示例3: 画圆轨迹
```cpp
void draw_circle(int radius, int steps = 360) {
    const double PI = 3.141592654;
    for (int angle = 0; angle < steps; angle++) {
        double radian = angle * PI / 180.0;
        int x = (int)(radius * cos(radian));
        int y = (int)(radius * sin(radian));
        
        if (angle == 0) {
            kmNet_mouse_move(x, y);  // 移动到起始点
        } else {
            int prev_x = (int)(radius * cos((angle-1) * PI / 180.0));
            int prev_y = (int)(radius * sin((angle-1) * PI / 180.0));
            kmNet_mouse_move(x - prev_x, y - prev_y);  // 相对移动
        }
        Sleep(10);
    }
}
```

### 示例4: 监听鼠标事件
```cpp
void monitor_mouse_events() {
    kmNet_monitor(1000);  // 启动监听
    
    while (true) {
        if (kmNet_monitor_mouse_left() == 1) {
            std::cout << "检测到左键按下" << std::endl;
            while (kmNet_monitor_mouse_left() == 1) {
                Sleep(1);  // 等待松开
            }
            std::cout << "左键松开" << std::endl;
        }
        
        int x, y;
        if (kmNet_monitor_mouse_xy(&x, &y) == 0) {
            static int last_x = 0, last_y = 0;
            if (x != last_x || y != last_y) {
                std::cout << "鼠标移动到: (" << x << ", " << y << ")" << std::endl;
                last_x = x; last_y = y;
            }
        }
        
        Sleep(10);  // 避免CPU占用过高
    }
}
```

## 错误处理

### 常见错误码
- `0`: 成功
- `err_creat_socket (-9000)`: 创建socket失败
- `err_net_version (-8999)`: socket版本错误
- `err_net_tx (-8998)`: socket发送错误
- `err_net_rx_timeout (-8997)`: socket接收超时
- `err_net_cmd (-8996)`: 命令错误
- `err_net_pts (-8995)`: 时间戳错误

### 错误处理示例
```cpp
int ret = kmNet_mouse_move(100, 50);
if (ret != 0) {
    switch (ret) {
        case err_net_tx:
            std::cout << "网络发送失败" << std::endl;
            break;
        case err_net_rx_timeout:
            std::cout << "网络接收超时" << std::endl;
            break;
        default:
            std::cout << "未知错误: " << ret << std::endl;
    }
    return false;
}
```

## 最佳实践

### 1. 连接管理
```cpp
class KMBoxController {
private:
    bool connected = false;
    
public:
    bool connect(const std::string& ip, const std::string& port, const std::string& mac) {
        int ret = kmNet_init((char*)ip.c_str(), (char*)port.c_str(), (char*)mac.c_str());
        connected = (ret == 0);
        return connected;
    }
    
    bool isConnected() const { return connected; }
};
```

### 2. 安全的鼠标操作
```cpp
bool safe_mouse_move(int x, int y) {
    int ret = kmNet_mouse_move(x, y);
    if (ret != 0) {
        std::cerr << "鼠标移动失败: " << ret << std::endl;
        return false;
    }
    return true;
}
```

### 3. 使用加密API
```cpp
// 推荐使用加密版本的API，防止数据包被分析
kmNet_enc_mouse_move(x, y);
kmNet_enc_mouse_left(1);
```

## 编译配置

### CMakeLists.txt 示例
```cmake
# 添加源文件
add_executable(my_program 
    main.cpp
    NetConfig/kmboxNet.cpp
    NetConfig/my_enc.cpp
)

# 包含头文件目录
target_include_directories(my_program PRIVATE NetConfig)

# 链接Windows库
if(WIN32)
    target_link_libraries(my_program ws2_32 winmm)
endif()
```

### 手动编译命令
```bash
# Windows (Visual Studio)
cl /EHsc main.cpp NetConfig\kmboxNet.cpp NetConfig\my_enc.cpp /I"NetConfig" /link ws2_32.lib

# Windows (MinGW)
g++ -std=c++11 main.cpp NetConfig/kmboxNet.cpp NetConfig/my_enc.cpp -INetConfig -lws2_32 -o my_program.exe
```

## 注意事项

1. **网络要求**: 确保电脑和KMBox设备在同一网络
2. **防火墙**: 可能需要关闭Windows防火墙或添加程序例外
3. **设备信息**: IP、端口、MAC地址必须与设备显示屏上的信息一致
4. **线程安全**: API支持多线程，但建议在主线程中调用
5. **性能**: 避免过于频繁的调用，建议间隔至少1ms
6. **坐标系**: 所有移动都是相对移动，不是绝对坐标

## 故障排除

### 连接问题
1. 检查设备IP、端口、MAC是否正确
2. 确保设备和电脑在同一网络
3. 尝试ping设备IP地址
4. 关闭防火墙重试

### 监听问题
1. 确保监听端口未被占用
2. 以管理员权限运行程序
3. 检查防火墙设置

### 性能问题
1. 减少调用频率
2. 使用批量操作API
3. 避免在循环中频繁调用

## 高级集成示例

### 完整的鼠标控制类
```cpp
#include "NetConfig/kmboxNet.h"
#include <string>
#include <iostream>

class KMBoxMouseController {
private:
    bool connected;
    std::string device_ip;
    std::string device_port;
    std::string device_mac;

public:
    KMBoxMouseController() : connected(false) {}

    // 连接设备
    bool connect(const std::string& ip, const std::string& port, const std::string& mac) {
        device_ip = ip;
        device_port = port;
        device_mac = mac;

        int ret = kmNet_init((char*)ip.c_str(), (char*)port.c_str(), (char*)mac.c_str());
        connected = (ret == 0);

        if (connected) {
            std::cout << "KMBox连接成功: " << ip << ":" << port << std::endl;
        } else {
            std::cout << "KMBox连接失败，错误码: " << ret << std::endl;
        }
        return connected;
    }

    // 检查连接状态
    bool isConnected() const { return connected; }

    // 安全的鼠标移动
    bool moveMouseSafe(int x, int y) {
        if (!connected) return false;
        int ret = kmNet_enc_mouse_move(x, y);  // 使用加密版本
        return ret == 0;
    }

    // 点击操作
    bool clickLeft(int hold_ms = 100) {
        if (!connected) return false;
        kmNet_enc_mouse_left(1);
        Sleep(hold_ms);
        kmNet_enc_mouse_left(0);
        return true;
    }

    bool clickRight(int hold_ms = 100) {
        if (!connected) return false;
        kmNet_enc_mouse_right(1);
        Sleep(hold_ms);
        kmNet_enc_mouse_right(0);
        return true;
    }

    // 平滑移动到目标位置
    bool moveToPosition(int x, int y, int duration_ms = 500) {
        if (!connected) return false;
        int ret = kmNet_enc_mouse_move_auto(x, y, duration_ms);
        return ret == 0;
    }

    // 拖拽操作
    bool dragMouse(int start_x, int start_y, int end_x, int end_y, int duration_ms = 500) {
        if (!connected) return false;

        // 移动到起始位置
        moveMouseSafe(start_x, start_y);
        Sleep(100);

        // 按下左键
        kmNet_enc_mouse_left(1);
        Sleep(50);

        // 拖拽到目标位置
        moveToPosition(end_x - start_x, end_y - start_y, duration_ms);
        Sleep(100);

        // 松开左键
        kmNet_enc_mouse_left(0);
        return true;
    }

    // 滚轮操作
    bool scroll(int direction, int steps = 3) {
        if (!connected) return false;
        for (int i = 0; i < steps; i++) {
            kmNet_enc_mouse_wheel(direction > 0 ? 3 : -3);
            Sleep(50);
        }
        return true;
    }
};
```

### 在游戏中的应用示例
```cpp
// 游戏自动化控制示例
class GameController {
private:
    KMBoxMouseController kmbox;

public:
    bool initialize(const std::string& ip, const std::string& port, const std::string& mac) {
        return kmbox.connect(ip, port, mac);
    }

    // 游戏中的点击攻击
    void attack() {
        kmbox.clickLeft(50);  // 快速点击
        Sleep(100);
    }

    // 技能释放（点击技能栏）
    void useSkill(int skill_x, int skill_y) {
        kmbox.moveToPosition(skill_x, skill_y, 200);  // 快速移动到技能位置
        Sleep(50);
        kmbox.clickLeft(50);
        Sleep(200);
    }

    // 物品拾取
    void pickupItem(int item_x, int item_y) {
        kmbox.moveToPosition(item_x, item_y, 300);
        Sleep(100);
        kmbox.clickRight(100);  // 右键拾取
    }

    // 视角控制
    void rotateView(int delta_x, int delta_y) {
        kmbox.moveMouseSafe(delta_x, delta_y);
    }
};
```

### 办公自动化示例
```cpp
// 办公自动化控制
class OfficeAutomation {
private:
    KMBoxMouseController kmbox;

public:
    bool initialize(const std::string& ip, const std::string& port, const std::string& mac) {
        return kmbox.connect(ip, port, mac);
    }

    // 文件操作
    void openFile(int file_x, int file_y) {
        kmbox.moveToPosition(file_x, file_y, 300);
        Sleep(100);
        kmbox.clickLeft(50);   // 单击选中
        Sleep(200);
        kmbox.clickLeft(50);   // 双击打开
    }

    // 复制粘贴操作（配合键盘）
    void copyText(int start_x, int start_y, int end_x, int end_y) {
        // 拖拽选择文本
        kmbox.dragMouse(start_x, start_y, end_x, end_y, 500);
        Sleep(200);
        // 这里可以配合键盘API发送Ctrl+C
    }

    // 滚动浏览文档
    void scrollDocument(bool down = true, int steps = 5) {
        kmbox.scroll(down ? -1 : 1, steps);
    }
};
```

## 与其他语言的集成

### Python调用示例（通过ctypes）
```python
import ctypes
import os

# 加载KMBox动态库
kmbox_dll = ctypes.CDLL('./kmbox_core.dll')

# 定义函数原型
kmbox_dll.kmNet_init.argtypes = [ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p]
kmbox_dll.kmNet_init.restype = ctypes.c_int

kmbox_dll.kmNet_mouse_move.argtypes = [ctypes.c_short, ctypes.c_short]
kmbox_dll.kmNet_mouse_move.restype = ctypes.c_int

# 连接设备
def connect_kmbox(ip, port, mac):
    result = kmbox_dll.kmNet_init(ip.encode(), port.encode(), mac.encode())
    return result == 0

# 移动鼠标
def move_mouse(x, y):
    return kmbox_dll.kmNet_mouse_move(x, y) == 0

# 使用示例
if connect_kmbox("*************", "8808", "62587019"):
    print("连接成功")
    move_mouse(100, 50)
else:
    print("连接失败")
```

### C#调用示例
```csharp
using System;
using System.Runtime.InteropServices;

public class KMBoxController
{
    [DllImport("kmbox_core.dll", CallingConvention = CallingConvention.Cdecl)]
    public static extern int kmNet_init(string ip, string port, string mac);

    [DllImport("kmbox_core.dll", CallingConvention = CallingConvention.Cdecl)]
    public static extern int kmNet_mouse_move(short x, short y);

    [DllImport("kmbox_core.dll", CallingConvention = CallingConvention.Cdecl)]
    public static extern int kmNet_mouse_left(int isdown);

    public static bool Connect(string ip, string port, string mac)
    {
        return kmNet_init(ip, port, mac) == 0;
    }

    public static bool MoveMouse(int x, int y)
    {
        return kmNet_mouse_move((short)x, (short)y) == 0;
    }

    public static void ClickLeft()
    {
        kmNet_mouse_left(1);
        System.Threading.Thread.Sleep(100);
        kmNet_mouse_left(0);
    }
}
```

## 性能优化建议

### 1. 批量操作
```cpp
// 避免频繁的单次调用
void inefficient_movement() {
    for (int i = 0; i < 100; i++) {
        kmNet_mouse_move(1, 0);  // 每次只移动1像素，效率低
        Sleep(10);
    }
}

// 推荐：使用较大的移动步长
void efficient_movement() {
    for (int i = 0; i < 10; i++) {
        kmNet_mouse_move(10, 0);  // 每次移动10像素，减少调用次数
        Sleep(10);
    }
}
```

### 2. 使用自动移动API
```cpp
// 推荐：使用自动移动API实现平滑移动
kmNet_mouse_move_auto(100, 50, 500);  // 一次调用完成平滑移动
```

### 3. 错误处理和重连机制
```cpp
class RobustKMBoxController {
private:
    std::string ip, port, mac;
    bool connected;
    int retry_count;

public:
    bool connectWithRetry(const std::string& ip, const std::string& port, const std::string& mac, int max_retries = 3) {
        this->ip = ip;
        this->port = port;
        this->mac = mac;

        for (int i = 0; i < max_retries; i++) {
            int ret = kmNet_init((char*)ip.c_str(), (char*)port.c_str(), (char*)mac.c_str());
            if (ret == 0) {
                connected = true;
                return true;
            }
            std::cout << "连接尝试 " << (i+1) << " 失败，错误码: " << ret << std::endl;
            Sleep(1000);  // 等待1秒后重试
        }
        connected = false;
        return false;
    }

    bool executeWithRetry(std::function<int()> operation, int max_retries = 2) {
        for (int i = 0; i < max_retries; i++) {
            int ret = operation();
            if (ret == 0) return true;

            // 如果是网络错误，尝试重连
            if (ret == err_net_tx || ret == err_net_rx_timeout) {
                std::cout << "网络错误，尝试重连..." << std::endl;
                if (connectWithRetry(ip, port, mac, 1)) {
                    continue;  // 重连成功，重试操作
                }
            }
        }
        return false;
    }
};
```

通过以上完整的API指南和示例，任何开发者都可以快速理解和集成KMBoxNet功能，实现精确的鼠标控制。无论是游戏自动化、办公自动化还是其他应用场景，都能找到合适的解决方案。
