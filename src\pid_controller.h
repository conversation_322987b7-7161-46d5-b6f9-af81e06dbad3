#pragma once

#include <chrono>
#include <array>

// PID控制器配置参数
struct PIDConfig {
    // 基础PID参数
    float kp = 2.5f;                    // 比例增益 (响应速度)
    float ki = 0.1f;                    // 积分增益 (消除稳态误差)
    float kd = 0.8f;                    // 微分增益 (减少超调)
    
    // 控制限制
    float max_output = 50.0f;           // 最大输出限制 (像素/帧)
    float min_output = 0.5f;            // 最小输出阈值
    float integral_limit = 100.0f;      // 积分限幅 (防止积分饱和)
    
    // 自适应参数
    bool adaptive_enabled = true;       // 是否启用自适应PID
    float distance_threshold = 100.0f;  // 距离阈值 (远近目标不同参数)
    float close_kp_multiplier = 0.6f;   // 近距离Kp倍数
    float far_kp_multiplier = 1.2f;     // 远距离Kp倍数
    
    // 平滑参数
    float smoothing_factor = 0.8f;      // 输出平滑因子 (0-1)
    bool velocity_prediction = true;    // 是否启用速度预测
    
    // 控制频率
    float control_frequency = 200.0f;   // 控制频率 (Hz)
};

// 目标状态信息
struct TargetState {
    float x = 0.0f;                     // 目标X坐标
    float y = 0.0f;                     // 目标Y坐标
    float velocity_x = 0.0f;            // X方向速度
    float velocity_y = 0.0f;            // Y方向速度
    bool valid = false;                 // 目标是否有效
    std::chrono::steady_clock::time_point timestamp; // 时间戳
};

// PID控制器类
class PIDController {
private:
    PIDConfig config_;
    
    // PID状态变量
    float integral_x_;                  // X轴积分累积
    float integral_y_;                  // Y轴积分累积
    float last_error_x_;                // 上次X轴误差
    float last_error_y_;                // 上次Y轴误差
    float last_output_x_;               // 上次X轴输出
    float last_output_y_;               // 上次Y轴输出
    
    // 时间管理
    std::chrono::steady_clock::time_point last_update_time_;
    float sample_time_;                 // 采样时间
    
    // 目标跟踪
    TargetState current_target_;
    TargetState last_target_;
    bool target_lost_;
    int lost_frames_;
    
    // 自适应参数
    float adaptive_kp_;
    float adaptive_ki_;
    float adaptive_kd_;

public:
    PIDController(const PIDConfig& config = PIDConfig{});
    ~PIDController() = default;

    // 配置管理
    void UpdateConfig(const PIDConfig& config);
    PIDConfig GetConfig() const { return config_; }
    
    // 主要控制接口
    std::array<float, 2> Update(float target_x, float target_y, float crosshair_x, float crosshair_y);
    std::array<float, 2> UpdateWithTarget(const TargetState& target, float crosshair_x, float crosshair_y);
    
    // 控制器状态管理
    void Reset();                       // 重置PID状态
    void SetEnabled(bool enabled);      // 启用/禁用控制器
    bool IsEnabled() const { return enabled_; }
    
    // 目标跟踪
    void UpdateTargetState(float x, float y);
    void SetTargetLost();               // 标记目标丢失
    bool IsTargetValid() const { return current_target_.valid; }
    
    // 调试信息
    struct DebugInfo {
        float error_x, error_y;         // 当前误差
        float p_term_x, p_term_y;       // 比例项
        float i_term_x, i_term_y;       // 积分项
        float d_term_x, d_term_y;       // 微分项
        float output_x, output_y;       // 最终输出
        float distance;                 // 目标距离
        float adaptive_kp;              // 自适应Kp值
        bool target_valid;              // 目标有效性
    };
    
    DebugInfo GetDebugInfo() const { return debug_info_; }

private:
    bool enabled_;
    DebugInfo debug_info_;
    
    // 内部计算函数
    void UpdateAdaptiveParameters(float distance);
    float ApplyLimits(float value, float min_val, float max_val);
    float CalculateDistance(float dx, float dy);
    void UpdateTargetVelocity();
    std::array<float, 2> PredictTargetPosition(float dt);
    float SmoothOutput(float current, float previous, float factor);
};
