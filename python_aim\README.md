# AI辅助瞄准程序 - Python版本

## 🎯 程序简介

这是一个基于深度学习的AI辅助瞄准程序，使用ONNX模型进行目标检测，支持多种鼠标控制方式和灵活的配置系统。

## ⚡ 快速开始

### 方法一：使用批处理脚本（推荐）

1. 双击运行 `安装依赖.bat` 安装Python依赖包
2. 双击运行 `快速启动.bat` 启动程序

### 方法二：手动安装

1. 确保已安装Python 3.8-3.11
2. 打开命令行，执行：
   ```bash
   pip install -r requirements.txt
   python main_enhanced.py
   ```

## 📁 文件结构

```
python_deploy_package/
├── main_enhanced.py          # 主程序
├── config.json              # 配置文件
├── config_example.json      # 配置示例
├── requirements.txt         # Python依赖
├── 部署说明.md              # 详细部署说明
├── 快速启动.bat             # 快速启动脚本
├── 安装依赖.bat             # 依赖安装脚本
├── bin/                     # 二进制库文件
├── models/                  # ONNX模型文件
└── [其他Python模块文件]
```

## ⚙️ 配置说明

### 基本配置

编辑 `config.json` 文件：

1. **模型设置**：
   - `model.path`: ONNX模型文件路径
   - `model.model_type`: 模型类型（yolov5/yolov8）

2. **鼠标控制**：
   - `mouse.move_mode`: 移动方式（system/ghub/kmbox）
   - `mouse.aim_keys`: 自瞄按键配置

3. **按键说明**：
   - `1`: 鼠标左键
   - `2`: 鼠标右键
   - `5`: 鼠标侧键1
   - `6`: 鼠标侧键2

### 高级配置

- **PID参数调节**：在 `pid` 部分调整不同移动方式的PID参数
- **显示设置**：在 `display` 部分配置预览窗口和字体
- **性能调优**：在 `debug` 部分启用性能监控

## 🎮 使用方法

1. 启动程序后会显示预览窗口
2. 按下配置的自瞄按键（默认鼠标侧键）开始瞄准
3. 程序会自动识别目标并移动鼠标到目标位置
4. 按ESC键或Ctrl+C退出程序

## 🔧 故障排除

### 常见问题

1. **Python环境问题**
   - 确保Python版本在3.8-3.11之间
   - 使用 `python --version` 检查版本

2. **依赖包问题**
   - 运行 `安装依赖.bat` 自动安装
   - 或手动执行 `pip install -r requirements.txt`

3. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确保模型文件完整未损坏

4. **鼠标移动无效**
   - 检查 `move_mode` 配置
   - 确保有足够的系统权限

### 性能优化

1. **提高检测速度**：
   - 降低模型分辨率
   - 使用GPU加速：`pip install onnxruntime-gpu`

2. **提高移动精度**：
   - 调整PID参数
   - 增加控制频率

## 📋 系统要求

- **操作系统**: Windows 10/11 (64位)
- **Python**: 3.8 - 3.11
- **内存**: 至少4GB RAM
- **显卡**: 支持DirectX 11（推荐NVIDIA显卡）

## ⚠️ 重要提醒

1. 本程序仅供学习研究使用
2. 请遵守相关游戏的使用条款
3. 建议在测试环境中验证功能
4. 使用前请备份重要数据

## 🔄 版本特性

- ✅ 多线程架构，高性能处理
- ✅ 支持多种ONNX模型格式
- ✅ 灵活的配置系统
- ✅ 多种鼠标控制方式
- ✅ 实时性能监控
- ✅ 智能目标选择算法
- ✅ 自动扳机功能（可选）

## 📞 技术支持

如遇问题，请检查：
1. 配置文件格式是否正确
2. 模型文件是否完整
3. Python环境是否正确安装
4. 系统权限是否足够

---

**祝您使用愉快！** 🎯
