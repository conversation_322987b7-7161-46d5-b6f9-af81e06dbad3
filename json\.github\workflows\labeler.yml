name: "Pull Request Labeler"

on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  label:
    permissions:
      contents: read
      pull-requests: write

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: srvaroa/labeler@e7bef2249506ba9cbbd3ca5cee256abd9f930b04 # master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
