import re
import os

def update_config():
    """更新example_capture.py中的KMBox配置参数"""
    print("=== KMBox配置更新工具 ===")
    print("此工具将帮助您更新KMBox的连接参数\n")
    
    # 检查文件是否存在
    if not os.path.exists("example_capture.py"):
        print("错误: 未找到example_capture.py文件！")
        return False
    
    # 读取当前配置
    with open("example_capture.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 提取当前配置
    ip_match = re.search(r'KMBOX_IP\s*=\s*["\']([^"\']+)["\']', content)
    port_match = re.search(r'KMBOX_PORT\s*=\s*["\']([^"\']+)["\']', content)
    mac_match = re.search(r'KMBOX_MAC\s*=\s*["\']([^"\']+)["\']', content)
    
    current_ip = ip_match.group(1) if ip_match else "*************"
    current_port = port_match.group(1) if port_match else "8808"
    current_mac = mac_match.group(1) if mac_match else "62587019"
    
    print(f"当前配置: IP={current_ip}, 端口={current_port}, MAC={current_mac}\n")
    
    # 获取新配置
    new_ip = input(f"输入新的IP地址 (留空使用当前值 {current_ip}): ") or current_ip
    new_port = input(f"输入新的端口号 (留空使用当前值 {current_port}): ") or current_port
    new_mac = input(f"输入新的MAC地址 (留空使用当前值 {current_mac}): ") or current_mac
    
    if new_ip == current_ip and new_port == current_port and new_mac == current_mac:
        print("\n配置未更改，无需保存。")
        return True
    
    # 更新配置
    updated_content = content
    updated_content = re.sub(
        r'KMBOX_IP\s*=\s*["\']([^"\']+)["\']', 
        f'KMBOX_IP = "{new_ip}"', 
        updated_content
    )
    updated_content = re.sub(
        r'KMBOX_PORT\s*=\s*["\']([^"\']+)["\']', 
        f'KMBOX_PORT = "{new_port}"', 
        updated_content
    )
    updated_content = re.sub(
        r'KMBOX_MAC\s*=\s*["\']([^"\']+)["\']', 
        f'KMBOX_MAC = "{new_mac}"', 
        updated_content
    )
    
    # 备份原文件
    backup_filename = "example_capture.py.bak"
    try:
        with open(backup_filename, "w", encoding="utf-8") as f:
            f.write(content)
        print(f"原文件已备份为 {backup_filename}")
    except Exception as e:
        print(f"备份文件失败: {e}")
    
    # 保存更新后的文件
    try:
        with open("example_capture.py", "w", encoding="utf-8") as f:
            f.write(updated_content)
        print("\n配置已成功更新！")
        print(f"新配置: IP={new_ip}, 端口={new_port}, MAC={new_mac}")
        return True
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

if __name__ == "__main__":
    update_config()
    print("\n按Enter键退出...")
    input() 