import time
import socket
import subprocess
import sys

print("=== KMBox 硬件连接诊断工具 ===")
print("本工具将帮助排查KMBox硬件连接问题\n")

# 步骤1: 检查kmNet模块是否可用
print("步骤1: 检查kmNet模块")
try:
    import kmNet
    print("✓ kmNet模块导入成功")
except ImportError as e:
    print(f"✗ kmNet模块导入失败: {e}")
    print("\n可能的解决方案:")
    print("1. 确认kmNet.pyd文件已经放在正确的目录中 (python环境的DLLs目录)")
    print("2. 确认kmNet.pyd文件版本与Python版本匹配")
    print("3. 尝试重新安装Python环境")
    sys.exit(1)

# 步骤2: 获取连接参数
print("\n步骤2: 配置KMBox连接参数")
ip = input("请输入KMBox的IP地址 (留空默认使用*************): ") or "*************"
port = input("请输入KMBox的端口号 (留空默认使用8808): ") or "8808"
mac = input("请输入KMBox的MAC地址 (留空默认使用62587019): ") or "62587019"

print(f"\n使用的连接参数: IP={ip}, 端口={port}, MAC={mac}")

# 步骤3: 检查网络连接
print("\n步骤3: 检查网络连接")
print(f"正在测试与KMBox ({ip}) 的网络连通性...")

try:
    # 测试ping
    ping_param = "-n" if sys.platform.lower() == "win32" else "-c"
    ping_cmd = ["ping", ping_param, "1", ip]
    ping_result = subprocess.run(ping_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=5)
    
    if "TTL=" in ping_result.stdout or "ttl=" in ping_result.stdout:
        print(f"✓ 成功ping通KMBox ({ip})")
    else:
        print(f"✗ 无法ping通KMBox ({ip})")
        print(ping_result.stdout)
        print("\n可能的解决方案:")
        print("1. 检查KMBox是否已接通电源")
        print("2. 确认KMBox已连接到与您相同的网络")
        print("3. 检查网络连接和防火墙设置")
except Exception as e:
    print(f"✗ Ping测试出错: {e}")

# 步骤4: 测试端口连接
print("\n步骤4: 测试端口连接")
try:
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(2)
        result = s.connect_ex((ip, int(port)))
        if result == 0:
            print(f"✓ 端口 {port} 连接成功")
        else:
            print(f"✗ 端口 {port} 无法连接 (错误代码: {result})")
            print("\n可能的解决方案:")
            print("1. 确认端口号正确无误")
            print("2. 检查KMBox是否已启动并正确配置")
            print("3. 确认防火墙允许此端口通信")
except Exception as e:
    print(f"✗ 端口测试出错: {e}")

# 步骤5: 尝试初始化KMBox
print("\n步骤5: 尝试初始化KMBox")
try:
    start_time = time.time()
    result = kmNet.init(ip, port, mac)
    end_time = time.time()
    
    print(f"初始化耗时: {(end_time - start_time)*1000:.2f}ms")
    
    if result == 0:
        print(f"✓ KMBox初始化成功!")
    else:
        print(f"✗ KMBox初始化失败，错误代码: {result}")
        # 常见错误代码解释
        error_codes = {
            1: "参数错误或格式不正确",
            2: "连接超时",
            3: "连接被拒绝",
            4: "认证失败",
            5: "设备忙",
            6: "通信错误"
        }
        if result in error_codes:
            print(f"错误说明: {error_codes.get(result, '未知错误')}")
        
        print("\n可能的解决方案:")
        print("1. 确认IP、端口和MAC参数与设备上显示的完全一致")
        print("2. 重启KMBox硬件设备")
        print("3. 确保KMBox固件是最新版本")
        print("4. 检查网络稳定性，确保不受防火墙限制")
except Exception as e:
    print(f"✗ 初始化过程异常: {e}")
    print("\n可能的解决方案:")
    print("1. 检查kmNet模块是否完整")
    print("2. 确认Python环境正确")

# 步骤6: 尝试简单的鼠标操作（如果初始化成功）
if 'result' in locals() and result == 0:
    print("\n步骤6: 测试基本鼠标操作")
    
    try:
        print("正在测试鼠标移动...")
        move_result = kmNet.move(10, 10)
        print(f"移动结果: {move_result}")
        
        print("正在测试左键点击...")
        kmNet.left(1)  # 按下
        time.sleep(0.1)
        kmNet.left(0)  # 松开
        print("左键点击完成")
        
        print("✓ 基本鼠标操作测试成功")
    except Exception as e:
        print(f"✗ 鼠标操作测试失败: {e}")

print("\n=== 诊断完成 ===")
print("请根据上述测试结果检查并修复KMBox连接问题")
print("如果需要更改连接参数，请修改example_capture.py中的以下变量:")
print("KMBOX_IP, KMBOX_PORT, KMBOX_MAC") 