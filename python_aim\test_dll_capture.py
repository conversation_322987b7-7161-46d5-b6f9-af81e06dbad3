# 文件: test_dll_capture.py

import ctypes
import cv2
import numpy as np
import time

# --- 1. 定义常量和结构 ---

# DLL 的路径
DLL_PATH = "bin/Dll1.dll"

# 截图方法枚举 (必须与 C++ 中的 Capture.h 保持一致)
METHOD_DXGI = 1

# --- 2. 加载 DLL 并设置函数签名 ---

try:
    # 加载我们编译好的DLL
    capture_lib = ctypes.CDLL(DLL_PATH)
except FileNotFoundError:
    print(f"错误: 找不到 {DLL_PATH}。")
    print("请确保已将编译好的 Dll1.dll 文件复制到此脚本所在的目录。")
    exit()

# -- create_capturer --
# CAPTURE_API void* create_capturer(CaptureMethod method, UINT32 capture_target);
create_capturer = capture_lib.create_capturer
create_capturer.argtypes = [ctypes.c_int, ctypes.c_uint32]
create_capturer.restype = ctypes.c_void_p

# -- release_capturer --
# CAPTURE_API void release_capturer(void* capturer_handle);
release_capturer = capture_lib.release_capturer
release_capturer.argtypes = [ctypes.c_void_p]
release_capturer.restype = None

# -- capture_frame --
# CAPTURE_API HRESULT capture_frame(void* handle, BYTE* p_buffer, UINT32 size, int* w, int* h);
capture_frame = capture_lib.capture_frame
capture_frame.argtypes = [
    ctypes.c_void_p,
    ctypes.POINTER(ctypes.c_byte),
    ctypes.c_uint32,
    ctypes.POINTER(ctypes.c_int),
    ctypes.POINTER(ctypes.c_int)
]
# HRESULT 在 ctypes 中通常是 c_long
capture_frame.restype = ctypes.c_long

# --- 3. 主程序逻辑 ---

print("--- C++ DLL 高性能截图测试 (带中心裁剪) ---")
print("--- 按下 'ESC' 键退出 ---")

# 创建一个DXGI截图器，目标是主显示器 (索引为 0)
capturer_handle = create_capturer(METHOD_DXGI, 0)

if not capturer_handle:
    print("创建截图器失败。请检查C++代码的Init()部分是否有错误。")
    exit()

# --- 定义你想要的截图区域 ---
# 目标：从屏幕中心截取一个 640x640 的区域
TARGET_SIZE = 640

# 预先分配一个足够大的缓冲区来接收图像数据
# 假设最大分辨率为 4K (3840x2160)，每个像素4字节(BGRA)
BUFFER_SIZE = 3840 * 2160 * 4
buffer = (ctypes.c_byte * BUFFER_SIZE)()

# 用于接收图像宽高的变量
width = ctypes.c_int()
height = ctypes.c_int()

# 用于计算FPS
start_time = time.perf_counter()
frame_count = 0

# 用于存储裁剪坐标
left, top, right, bottom = 0, 0, 0, 0
is_first_frame = True

try:
    while True:
        # 调用DLL中的截图函数
        hr = capture_frame(
            capturer_handle,
            buffer,
            BUFFER_SIZE,
            ctypes.byref(width),
            ctypes.byref(height)
        )

        if hr >= 0 and width.value > 0 and height.value > 0: # S_OK and S_FALSE are >= 0
            
            # 第一次获取到图像时，计算裁剪区域
            if is_first_frame:
                screen_w, screen_h = width.value, height.value
                left = (screen_w - TARGET_SIZE) // 2
                top = (screen_h - TARGET_SIZE) // 2
                right = left + TARGET_SIZE
                bottom = top + TARGET_SIZE
                print(f"全屏尺寸: {screen_w}x{screen_h}")
                print(f"将裁剪区域: ({left}, {top}) 到 ({right}, {bottom})")
                is_first_frame = False

            # 从 ctypes 缓冲区创建一个 NumPy 数组视图，无需内存拷贝！
            # 使用 np.frombuffer 再 reshape 是更可靠的方法
            # 通过 count 参数指定只读取 DLL 实际写入的数据量
            actual_size = height.value * width.value * 4
            full_screen_img = np.frombuffer(buffer, dtype=np.uint8, count=actual_size).reshape(
                (height.value, width.value, 4)
            )

            # --- 核心裁剪步骤 ---
            # 使用NumPy切片，高效提取ROI (Region of Interest)
            # 注意：NumPy的索引是 [行, 列] 或 [y, x]
            cropped_img = full_screen_img[top:bottom, left:right]

            # 计算FPS
            frame_count += 1
            elapsed = time.perf_counter() - start_time
            if elapsed >= 1.0:
                fps = frame_count / elapsed
                print(f"FPS: {fps:.2f}")
                frame_count = 0
                start_time = time.perf_counter()

            # 显示裁剪后的图像
            cv2.imshow("DLL Capture - Cropped", cropped_img)
        
        # 等待按键，如果按下ESC (ASCII码 27) 则退出
        if cv2.waitKey(1) == 27:
            break
            
finally:
    # --- 4. 清理资源 ---
    print("正在释放资源...")
    if capturer_handle:
        release_capturer(capturer_handle)
    cv2.destroyAllWindows()
    print("测试结束。")
