#include "pid_controller.h"
#include <cmath>
#include <algorithm>
#include <iostream>

PIDController::PIDController(const PIDConfig& config) 
    : config_(config), enabled_(true), target_lost_(false), lost_frames_(0) {
    
    // 初始化PID状态
    Reset();
    
    // 计算采样时间
    sample_time_ = 1.0f / config_.control_frequency;
    
    std::cout << "[PID控制器] 初始化完成" << std::endl;
    std::cout << "[PID控制器] Kp=" << config_.kp << ", Ki=" << config_.ki << ", Kd=" << config_.kd << std::endl;
    std::cout << "[PID控制器] 控制频率=" << config_.control_frequency << "Hz" << std::endl;
    std::cout << "[PID控制器] 自适应控制=" << (config_.adaptive_enabled ? "启用" : "禁用") << std::endl;
}

void PIDController::UpdateConfig(const PIDConfig& config) {
    config_ = config;
    sample_time_ = 1.0f / config_.control_frequency;
    
    std::cout << "[PID控制器] 配置已更新" << std::endl;
}

void PIDController::Reset() {
    integral_x_ = 0.0f;
    integral_y_ = 0.0f;
    last_error_x_ = 0.0f;
    last_error_y_ = 0.0f;
    last_output_x_ = 0.0f;
    last_output_y_ = 0.0f;
    
    current_target_.valid = false;
    last_target_.valid = false;
    target_lost_ = false;
    lost_frames_ = 0;
    
    last_update_time_ = std::chrono::steady_clock::now();
    
    // 重置自适应参数
    adaptive_kp_ = config_.kp;
    adaptive_ki_ = config_.ki;
    adaptive_kd_ = config_.kd;
}

void PIDController::SetEnabled(bool enabled) {
    enabled_ = enabled;
    if (!enabled) {
        Reset();
    }
}

std::array<float, 2> PIDController::Update(float target_x, float target_y, float crosshair_x, float crosshair_y) {
    if (!enabled_) {
        return {0.0f, 0.0f};
    }
    
    // 更新目标状态
    UpdateTargetState(target_x, target_y);
    
    // 计算时间差
    auto now = std::chrono::steady_clock::now();
    float dt = std::chrono::duration<float>(now - last_update_time_).count();
    
    // 限制最小时间间隔，避免除零
    if (dt < 0.001f) {
        return {last_output_x_, last_output_y_};
    }
    
    // 如果目标无效，返回零输出
    if (!current_target_.valid) {
        Reset();
        return {0.0f, 0.0f};
    }
    
    // 计算基础误差
    float error_x = target_x - crosshair_x;
    float error_y = target_y - crosshair_y;
    
    // 速度预测补偿
    if (config_.velocity_prediction && !target_lost_) {
        auto predicted_pos = PredictTargetPosition(dt);
        error_x += predicted_pos[0];
        error_y += predicted_pos[1];
    }
    
    // 计算距离并更新自适应参数
    float distance = CalculateDistance(error_x, error_y);
    if (config_.adaptive_enabled) {
        UpdateAdaptiveParameters(distance);
    }
    
    // 计算PID各项
    // 比例项 (P)
    float p_term_x = adaptive_kp_ * error_x;
    float p_term_y = adaptive_kp_ * error_y;
    
    // 积分项 (I)
    integral_x_ += error_x * dt;
    integral_y_ += error_y * dt;
    
    // 积分限幅，防止积分饱和
    integral_x_ = ApplyLimits(integral_x_, -config_.integral_limit, config_.integral_limit);
    integral_y_ = ApplyLimits(integral_y_, -config_.integral_limit, config_.integral_limit);
    
    float i_term_x = adaptive_ki_ * integral_x_;
    float i_term_y = adaptive_ki_ * integral_y_;
    
    // 微分项 (D)
    float d_error_x = (error_x - last_error_x_) / dt;
    float d_error_y = (error_y - last_error_y_) / dt;
    
    float d_term_x = adaptive_kd_ * d_error_x;
    float d_term_y = adaptive_kd_ * d_error_y;
    
    // 计算总输出
    float raw_output_x = p_term_x + i_term_x + d_term_x;
    float raw_output_y = p_term_y + i_term_y + d_term_y;
    
    // 输出限幅
    raw_output_x = ApplyLimits(raw_output_x, -config_.max_output, config_.max_output);
    raw_output_y = ApplyLimits(raw_output_y, -config_.max_output, config_.max_output);
    
    // 输出平滑
    float smooth_output_x = SmoothOutput(raw_output_x, last_output_x_, config_.smoothing_factor);
    float smooth_output_y = SmoothOutput(raw_output_y, last_output_y_, config_.smoothing_factor);
    
    // 最小输出阈值
    if (std::abs(smooth_output_x) < config_.min_output) smooth_output_x = 0.0f;
    if (std::abs(smooth_output_y) < config_.min_output) smooth_output_y = 0.0f;
    
    // 更新状态
    last_error_x_ = error_x;
    last_error_y_ = error_y;
    last_output_x_ = smooth_output_x;
    last_output_y_ = smooth_output_y;
    last_update_time_ = now;
    
    // 更新调试信息
    debug_info_.error_x = error_x;
    debug_info_.error_y = error_y;
    debug_info_.p_term_x = p_term_x;
    debug_info_.p_term_y = p_term_y;
    debug_info_.i_term_x = i_term_x;
    debug_info_.i_term_y = i_term_y;
    debug_info_.d_term_x = d_term_x;
    debug_info_.d_term_y = d_term_y;
    debug_info_.output_x = smooth_output_x;
    debug_info_.output_y = smooth_output_y;
    debug_info_.distance = distance;
    debug_info_.adaptive_kp = adaptive_kp_;
    debug_info_.target_valid = current_target_.valid;
    
    return {smooth_output_x, smooth_output_y};
}

std::array<float, 2> PIDController::UpdateWithTarget(const TargetState& target, float crosshair_x, float crosshair_y) {
    current_target_ = target;
    
    if (target.valid) {
        return Update(target.x, target.y, crosshair_x, crosshair_y);
    } else {
        SetTargetLost();
        return {0.0f, 0.0f};
    }
}

void PIDController::UpdateTargetState(float x, float y) {
    auto now = std::chrono::steady_clock::now();
    
    // 保存上一个目标状态
    last_target_ = current_target_;
    
    // 更新当前目标状态
    current_target_.x = x;
    current_target_.y = y;
    current_target_.valid = true;
    current_target_.timestamp = now;
    
    // 计算目标速度
    UpdateTargetVelocity();
    
    // 重置丢失计数
    target_lost_ = false;
    lost_frames_ = 0;
}

void PIDController::SetTargetLost() {
    target_lost_ = true;
    lost_frames_++;
    current_target_.valid = false;
    
    // 如果目标丢失太久，重置积分项
    if (lost_frames_ > 10) {
        integral_x_ *= 0.9f;  // 逐渐衰减积分项
        integral_y_ *= 0.9f;
    }
}

void PIDController::UpdateAdaptiveParameters(float distance) {
    if (distance < config_.distance_threshold) {
        // 近距离目标：降低增益，提高精度
        adaptive_kp_ = config_.kp * config_.close_kp_multiplier;
        adaptive_ki_ = config_.ki * 0.8f;
        adaptive_kd_ = config_.kd * 1.2f;
    } else {
        // 远距离目标：提高增益，加快响应
        adaptive_kp_ = config_.kp * config_.far_kp_multiplier;
        adaptive_ki_ = config_.ki * 1.2f;
        adaptive_kd_ = config_.kd * 0.8f;
    }
}

float PIDController::ApplyLimits(float value, float min_val, float max_val) {
    return std::max(min_val, std::min(max_val, value));
}

float PIDController::CalculateDistance(float dx, float dy) {
    return std::sqrt(dx * dx + dy * dy);
}

void PIDController::UpdateTargetVelocity() {
    if (!last_target_.valid) {
        current_target_.velocity_x = 0.0f;
        current_target_.velocity_y = 0.0f;
        return;
    }
    
    auto dt = std::chrono::duration<float>(current_target_.timestamp - last_target_.timestamp).count();
    if (dt > 0.001f) {
        current_target_.velocity_x = (current_target_.x - last_target_.x) / dt;
        current_target_.velocity_y = (current_target_.y - last_target_.y) / dt;
    }
}

std::array<float, 2> PIDController::PredictTargetPosition(float dt) {
    if (!current_target_.valid) {
        return {0.0f, 0.0f};
    }
    
    // 简单的线性预测
    float predict_x = current_target_.velocity_x * dt * 0.5f;  // 预测系数
    float predict_y = current_target_.velocity_y * dt * 0.5f;
    
    return {predict_x, predict_y};
}

float PIDController::SmoothOutput(float current, float previous, float factor) {
    return previous * (1.0f - factor) + current * factor;
}
