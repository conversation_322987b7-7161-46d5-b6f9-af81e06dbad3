#include "simple_aiming.h"
#include <cmath>
#include <algorithm>
#include <iostream>

SimpleAiming::SimpleAiming(const SimpleAimConfig& config) : config_(config) {
    std::cout << "[简化自瞄] 初始化完成" << std::endl;
    std::cout << "[简化自瞄] 自瞄圆环半径: " << config_.aim_circle_radius << std::endl;
    std::cout << "[简化自瞄] 主要目标类别: " << (config_.primary_class == 0 ? "头部" : "身体") << std::endl;
    std::cout << "[简化自瞄] 状态: " << (config_.enabled ? "启用" : "禁用") << std::endl;
}

void SimpleAiming::ProcessDetections(const std::vector<Detection>& detections) {
    if (!config_.enabled) {
        return;
    }
    
    if (!IsAimKeyPressed()) {
        return;
    }
    
    if (detections.empty()) {
        return;
    }
    
    Detection best_target = SelectBestTarget(detections);
    
    if (best_target.class_id < 0) {
        return;
    }
    
    MoveMouseToTarget(best_target);
}

bool SimpleAiming::IsAimKeyPressed() {
    return (GetAsyncKeyState(0x02) & 0x8000) != 0;
}

Detection SimpleAiming::SelectBestTarget(const std::vector<Detection>& detections) {
    float crosshair_x = static_cast<float>(config_.screen_center_x);
    float crosshair_y = static_cast<float>(config_.screen_center_y);
    
    std::vector<Detection> primary_targets;
    std::vector<Detection> secondary_targets;
    
    for (const auto& det : detections) {
        if (!IsInAimCircle(det, crosshair_x, crosshair_y)) {
            continue;
        }
        
        if (det.class_id == config_.primary_class) {
            primary_targets.push_back(det);
        } else if (det.class_id == config_.secondary_class) {
            secondary_targets.push_back(det);
        }
    }
    
    std::vector<Detection>* target_list = primary_targets.empty() ? &secondary_targets : &primary_targets;
    
    if (target_list->empty()) {
        Detection invalid_target = {};
        invalid_target.class_id = -1;
        return invalid_target;
    }
    
    std::sort(target_list->begin(), target_list->end(), 
              [this, crosshair_x, crosshair_y](const Detection& a, const Detection& b) {
                  return CalculateDistance(a, crosshair_x, crosshair_y) < 
                         CalculateDistance(b, crosshair_x, crosshair_y);
              });
    
    return (*target_list)[0];
}

float SimpleAiming::CalculateDistance(const Detection& detection, float crosshair_x, float crosshair_y) {
    float target_x = detection.x + detection.width / 2.0f;
    float target_y = detection.y + detection.height / 2.0f;
    
    float dx = target_x - crosshair_x;
    float dy = target_y - crosshair_y;
    return std::sqrt(dx * dx + dy * dy);
}

bool SimpleAiming::IsInAimCircle(const Detection& detection, float crosshair_x, float crosshair_y) {
    float distance = CalculateDistance(detection, crosshair_x, crosshair_y);
    return distance <= config_.aim_circle_radius;
}

void SimpleAiming::MoveMouseToTarget(const Detection& det) {
    // 简化版本：直接移动到目标中心
    float center_x = det.x + det.width / 2.0f;
    float center_y = det.y + det.height / 2.0f;

    int move_x = static_cast<int>(center_x - config_.screen_center_x);
    int move_y = static_cast<int>(center_y - config_.screen_center_y);

    if (MoveMouse(move_x, move_y)) {
        std::cout << "[简化自瞄] 移动鼠标: (" << move_x << ", " << move_y << ")" << std::endl;
    }
}

bool SimpleAiming::MoveMouse(int dx, int dy) {
    if (dx == 0 && dy == 0) return true;
    
    try {
        POINT current_pos;
        if (!GetCursorPos(&current_pos)) {
            return false;
        }
        
        int new_x = current_pos.x + dx;
        int new_y = current_pos.y + dy;
        
        return SetCursorPos(new_x, new_y) != 0;
    } catch (...) {
        return false;
    }
}
