# -*- coding: utf-8 -*-
# 文件名: postprocess.py
# 功能: 推理结果后处理模块

# --- 导入库 ---
# 导入OpenCV库，简称'cv2'。这里我们主要用它来画图，比如在图片上画框框和写字。
import cv2
# 导入NumPy库，简称'np'。模型输出的结果是NumPy数组，我们需要用它来做各种计算。
import numpy as np
# 从typing库中导入List和Tuple，这能帮助我们更清晰地定义函数需要什么样的数据，
# 以及会返回什么样的数据，让代码更容易看懂。
from typing import List, Tuple, Dict

# --- 定义后处理类 ---
# 定义一个名为'DetectionPostProcessor'的类，它像一个数据分析师，
# 专门负责解读AI模型给出的原始数据，并把它整理成清晰的可视化报告。
class DetectionPostProcessor:
    """
    目标检测结果后处理类，经过简化和重构，以提高健壮性。
    """
    def __init__(self, confidence_threshold: float = 0.5, nms_threshold: float = 0.1, class_names: List[str] = None):
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        self.class_names = class_names if class_names is not None else ["头部", "身体"]
        self.format_printed = False

    def process(self, output: any, original_img: np.ndarray, input_shape: Tuple[int, int]) -> Tuple[np.ndarray, List[Dict]]:
        """
        统一处理所有YOLO格式的模型输出。
        """
        # --- 1. 数据清洗和塑形 ---
        try:
            # a. 解包：应对嵌套元组输出
            tensor = output
            while isinstance(tensor, tuple):
                tensor = tensor[0]
            
            # b. 扶正：将 (batch, dims, boxes) 转置为 (batch, boxes, dims)
            if len(tensor.shape) == 3 and tensor.shape[1] < tensor.shape[2]:
                if not self.format_printed: print(f"检测到转置格式 {tensor.shape}, 正在自动扶正...")
                tensor = tensor.transpose(0, 2, 1)
                if not self.format_printed: print(f"扶正后形状: {tensor.shape}")
            
            # c. 展平：将 (batch, boxes, dims) 统一为 (N, dims) 格式
            predictions = np.squeeze(tensor, axis=0)
            
            if not self.format_printed:
                print(f"最终处理的张量形状: {predictions.shape}")
                self.format_printed = True

        except Exception as e:
            if not self.format_printed:
                print(f"数据清洗阶段出错: {e}, 输入类型: {type(output)}")
                self.format_printed = True
            return original_img, []

        # --- 2. 解析预测结果 ---
        # 检查数据维度是否有效
        if predictions.shape[1] < 5:
            return original_img, []

        # --- START: 采用更灵活的逻辑，兼容不同YOLO输出格式 ---
        # 提取边界框坐标
        boxes_xywh = predictions[:, :4]
        # 将第4列之后的所有数据都视为类别分数
        class_scores_all = predictions[:, 4:]

        # a. 对每个框，在其所有类别分数中找到最高分，作为其最终置信度
        scores = np.max(class_scores_all, axis=1)
        
        # b. 找到最高分对应的索引，作为其类别ID
        class_indices_all = np.argmax(class_scores_all, axis=1)

        # c. 根据置信度阈值创建掩码，进行过滤
        mask = scores > self.confidence_threshold
        if not np.any(mask):
            return original_img, []

        # d. 应用掩码，得到最终用于后续处理的数据
        filtered_boxes = boxes_xywh[mask]
        filtered_scores = scores[mask]
        # 关键：这里的变量名必须是 class_indices，以匹配后续代码
        class_indices = class_indices_all[mask]
        # --- END: 灵活逻辑结束 ---

        # --- 3. 坐标转换 ---
        img_h, img_w = original_img.shape[:2]
        
        # 检查坐标是否为归一化 (0-1之间)
        if np.max(filtered_boxes) <= 1.0:
            # (cx, cy, w, h) -> (x1, y1, x2, y2)
            cx, cy, w, h = filtered_boxes.T
            x1 = (cx - w / 2) * img_w
            y1 = (cy - h / 2) * img_h
            x2 = (cx + w / 2) * img_w
            y2 = (cy + h / 2) * img_h
            boxes_xyxy = np.stack((x1, y1, x2, y2), axis=1)
        else:
            # 如果是像素坐标 (cx, cy, w, h) -> (x1, y1, x2, y2)
            cx, cy, w, h = filtered_boxes.T
            x1 = cx - w / 2
            y1 = cy - h / 2
            x2 = cx + w / 2
            y2 = cy + h / 2
            boxes_xyxy = np.stack((x1, y1, x2, y2), axis=1)

        # --- 4. 按类别进行非极大值抑制 (Per-Class NMS) ---
        detections = []
        # 获取所有不重复的类别ID
        unique_class_ids = np.unique(class_indices)

        # 遍历每个类别ID
        for class_id in unique_class_ids:
            # a. 筛选出当前类别的所有检测结果
            # 创建一个布尔掩码，只选择当前类别的索引
            class_mask = (class_indices == class_id)
            # 应用掩码来获取当前类别的边界框和分数
            class_boxes = boxes_xyxy[class_mask]
            class_scores = filtered_scores[class_mask]

            # 如果这个类别没有检测到任何框，就跳到下一个类别
            if len(class_boxes) == 0:
                continue

            # b. 对当前类别的检测结果进行NMS
            # 这会返回保留下来的框的索引
            indices = cv2.dnn.NMSBoxes(class_boxes.tolist(), class_scores.tolist(), self.confidence_threshold, self.nms_threshold)

            # 如果NMS之后有保留下来的框
            if len(indices) > 0:
                # c. 从NMS结果中提取最终的检测信息
                # NMS返回的索引是相对于'class_boxes'的，所以我们用它来选择
                final_boxes_for_class = class_boxes[indices.flatten()]
                final_scores_for_class = class_scores[indices.flatten()]

                # 遍历这个类别中最终保留的每一个框
                for i in range(len(final_boxes_for_class)):
                    x1, y1, x2, y2 = final_boxes_for_class[i]
                    score = final_scores_for_class[i]
                    
                    # 将坐标裁剪到图像边界内，确保不会画到外面去
                    x = max(0, int(x1))
                    y = max(0, int(y1))
                    w = max(0, int(x2 - x1))
                    h = max(0, int(y2 - y1))
                    
                    # 确保 class_id 不会因为意外错误而越界
                    safe_class_id = min(int(class_id), len(self.class_names) - 1)
                    
                    # 将整理好的信息添加到一个字典中，然后存入列表
                    detections.append({
                        "class_id": safe_class_id,
                        "class_name": self.class_names[safe_class_id],
                        "confidence": float(score),
                        "box": [x, y, w, h]
                    })

        # --- 5. 绘制结果 ---
        # 经过所有类别的NMS处理后，我们得到了最终的检测列表
        result_image = original_img.copy()
        # 在图片上把这些框都画出来
        self.draw_detections(result_image, detections)
        
        return result_image, detections

    def draw_detections(self, image: np.ndarray, detections: List[Dict]) -> None:
        colors = {0: (0, 0, 255), 1: (0, 255, 0)} # BGR
        for det in detections:
            box = det["box"]
            x, y, w, h = box
            class_id = det["class_id"]
            label = f"{det['class_name']}: {det['confidence']:.2f}"
            color = colors.get(class_id, (255, 255, 0))
            
            cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
            cv2.putText(image, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# --- 程序主入口 ---
if __name__ == "__main__":
    
    print("--- 这是一个测试程序，将使用模拟数据来测试后处理功能 ---")

    # --- 创建对象 ---
    # 创建一个后处理器实例。
    post_processor = DetectionPostProcessor(
        confidence_threshold=0.5, # 只有置信度大于0.5的才算数
        nms_threshold=0.4         # 两个框重叠超过40%，就只留一个
    )
    
    # --- 准备测试数据 ---
    # 读取一张测试图片。
    print("正在读取测试图片 '1.jpg' ...")
    test_image = cv2.imread("1.jpg")

    if test_image is None:
        print("错误：无法读取测试图片'1.jpg'，请确保文件在同一个目录下！")
    else:
        print("测试图片读取成功！")
        # 创造一些假的AI模型输出数据，用来测试。
        # 形状是 (1, 3, 7)，表示有3个检测结果。
        # 每个结果的格式: [cx, cy, w, h, obj_conf, person_score, enemy_score]
        # (中心x比例, 中心y比例, 宽比例, 高比例, 物体置信度, 类别0分数, 类别1分数)
        fake_output = np.array([[
            # 检测结果1: 一个置信度很高的'person'
            [0.2, 0.3, 0.2, 0.3, 0.95, 0.9, 0.1],
            # 检测结果2: 一个与结果1重叠的'person'，但置信度稍低。NMS应该会把它去掉。
            [0.22, 0.32, 0.2, 0.3, 0.88, 0.8, 0.2],
            # 检测结果3: 一个在别处的'enemy'
            [0.7, 0.6, 0.3, 0.4, 0.98, 0.1, 0.9],
        ]])
        
        print("\n正在用模拟数据进行后处理...")
        # --- 调用核心处理方法 ---
        # 把假的输出和真图一起传给process方法。
        result_image, final_detections = post_processor.process(fake_output, test_image)
        
        # --- 打印和显示结果 ---
        print(f"\n经过NMS处理后，最终检测到 {len(final_detections)} 个目标:")
        # 遍历并打印出每一个最终结果的详细信息。
        for i, det in enumerate(final_detections):
            print(f"  - 目标 #{i+1}: "
                  f"类别='{det['class_name']}', "
                  f"置信度={det['confidence']:.2f}, "
                  f"位置(x,y,w,h)={det['box']}")
        
        # 把画好框的图片显示出来。
        cv2.imshow("后处理测试结果", result_image)
        
        # --- 等待用户操作 ---
        print("\n请在图像窗口按任意键退出...")
        # cv2.waitKey(0)会一直等待，直到用户按下任意一个键。
        cv2.waitKey(0)
        # 关闭所有OpenCV窗口。
        cv2.destroyAllWindows()
    
    print("--- 测试程序结束 ---") 