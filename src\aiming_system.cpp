#include "aiming_system.h"
#include <windows.h>
#include <cmath>
#include <algorithm>
#include <iostream>

// =============================================================================
// PID控制器实现
// =============================================================================

PIDController::PIDController(float kp, float ki, float kd, float sample_time)
    : kp_(kp), ki_(ki), kd_(kd), sample_time_(sample_time)
    , integral_(0), last_error_x_(0), last_error_y_(0) {
    last_time_ = std::chrono::steady_clock::now();
}

std::array<float, 2> PIDController::Compute(float error_x, float error_y) {
    auto now = std::chrono::steady_clock::now();
    auto dt = std::chrono::duration<float>(now - last_time_).count();
    
    if (dt < sample_time_) {
        return {0.0f, 0.0f};
    }
    
    float error_magnitude = std::sqrt(error_x * error_x + error_y * error_y);
    integral_ += error_magnitude * dt;
    
    float derivative_x = (error_x - last_error_x_) / dt;
    float derivative_y = (error_y - last_error_y_) / dt;
    
    float output_x = kp_ * error_x + ki_ * integral_ * (error_x / (error_magnitude + 1e-6f)) + kd_ * derivative_x;
    float output_y = kp_ * error_y + ki_ * integral_ * (error_y / (error_magnitude + 1e-6f)) + kd_ * derivative_y;
    
    last_error_x_ = error_x;
    last_error_y_ = error_y;
    last_time_ = now;
    
    return {output_x, output_y};
}

void PIDController::Reset() {
    integral_ = 0;
    last_error_x_ = 0;
    last_error_y_ = 0;
    last_time_ = std::chrono::steady_clock::now();
}

void PIDController::UpdateGains(float kp, float ki, float kd) {
    kp_ = kp;
    ki_ = ki;
    kd_ = kd;
}

// =============================================================================
// 目标选择器实现
// =============================================================================

Target TargetSelector::SelectBestTarget(const std::vector<Detection>& detections, 
                                       int aim_priority, 
                                       float crosshair_x, float crosshair_y) {
    if (aim_priority == 0 || detections.empty()) {
        return Target{};
    }
    
    std::vector<Target> primary_targets;
    std::vector<Target> secondary_targets;
    
    for (const auto& det : detections) {
        Target target(det);
        
        if (!IsInAimCircle(target, crosshair_x, crosshair_y)) {
            continue;
        }
        
        if (det.class_id == config_.primary_class) {
            primary_targets.push_back(target);
        } else if (det.class_id == config_.secondary_class) {
            secondary_targets.push_back(target);
        }
    }
    
    std::vector<Target>* target_list = nullptr;
    if (aim_priority == 1) {
        target_list = primary_targets.empty() ? &secondary_targets : &primary_targets;
    } else {
        target_list = secondary_targets.empty() ? &primary_targets : &secondary_targets;
    }
    
    if (target_list->empty()) {
        return Target{};
    }
    
    std::sort(target_list->begin(), target_list->end(), 
              [this, crosshair_x, crosshair_y](const Target& a, const Target& b) {
                  return CalculateDistance(a, crosshair_x, crosshair_y) < 
                         CalculateDistance(b, crosshair_x, crosshair_y);
              });
    
    return (*target_list)[0];
}

float TargetSelector::CalculateDistance(const Target& target, float crosshair_x, float crosshair_y) {
    float dx = target.x - crosshair_x;
    float dy = target.y - crosshair_y;
    return std::sqrt(dx * dx + dy * dy);
}

bool TargetSelector::IsInAimCircle(const Target& target, float crosshair_x, float crosshair_y) {
    float distance = CalculateDistance(target, crosshair_x, crosshair_y);
    return distance <= config_.aim_circle_radius;
}

// =============================================================================
// 鼠标控制器实现
// =============================================================================

bool MouseController::MoveMouse(int dx, int dy) {
    if (dx == 0 && dy == 0) return true;
    
    try {
        POINT current_pos;
        if (!GetCursorPos(&current_pos)) {
            return false;
        }
        
        int new_x = current_pos.x + dx;
        int new_y = current_pos.y + dy;
        
        return SetCursorPos(new_x, new_y) != 0;
    } catch (...) {
        return false;
    }
}

bool MouseController::LeftClick() {
    return LeftDown() && LeftUp();
}

bool MouseController::LeftDown() {
    try {
        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
        return true;
    } catch (...) {
        return false;
    }
}

bool MouseController::LeftUp() {
    try {
        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
        return true;
    } catch (...) {
        return false;
    }
}

// =============================================================================
// 扳机系统实现
// =============================================================================

TriggerSystem::TriggerSystem(const AimingConfig& config, MouseController* mouse_controller)
    : config_(config), trigger_active_(false), mouse_controller_(mouse_controller) {
    last_fire_time_ = std::chrono::steady_clock::now();
}

void TriggerSystem::CheckAndFire(float error_magnitude, bool has_valid_target, bool is_aiming) {
    if (!config_.trigger_enabled || !has_valid_target || !is_aiming) {
        return;
    }
    
    if (error_magnitude > config_.trigger_threshold) {
        return;
    }
    
    auto now = std::chrono::steady_clock::now();
    auto time_since_last_fire = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_fire_time_).count();
    
    if (time_since_last_fire < config_.fire_interval_ms) {
        return;
    }
    
    last_fire_time_ = now;
    std::thread(&TriggerSystem::FireAsync, this).detach();
}

void TriggerSystem::FireAsync() {
    if (trigger_active_.exchange(true)) {
        return;
    }
    
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.trigger_delay_ms));
        
        if (mouse_controller_) {
            mouse_controller_->LeftClick();
            std::cout << "[扳机] 自动开火!" << std::endl;
        }
    } catch (...) {
        std::cout << "[扳机] 开火失败!" << std::endl;
    }
    
    trigger_active_.store(false);
}

// =============================================================================
// 自瞄状态管理实现
// =============================================================================

AimingState::AimingState()
    : has_target_(false), target_updated_(false), is_aiming_(false) {
    total_error_ = {0.0f, 0.0f};
    remaining_error_ = {0.0f, 0.0f};
    running_.store(false);
}

void AimingState::UpdateTarget(const Target& target) {
    std::lock_guard<std::mutex> lock(mutex_);
    current_target_ = target;
    has_target_ = (target.class_id >= 0);
    target_updated_ = true;
}

void AimingState::ClearTarget() {
    std::lock_guard<std::mutex> lock(mutex_);
    current_target_ = Target{};
    has_target_ = false;
    target_updated_ = false;
}

void AimingState::UpdateAimStatus(bool is_aiming) {
    std::lock_guard<std::mutex> lock(mutex_);
    is_aiming_ = is_aiming;
}

AimingState::ControlInfo AimingState::GetControlInfo() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ControlInfo info;
    info.is_aiming = is_aiming_;
    info.total_error = total_error_;
    info.remaining_error = remaining_error_;
    info.new_target_available = target_updated_;
    info.has_valid_target = has_target_;
    
    target_updated_ = false;
    
    return info;
}

void AimingState::UpdateRemainingError(const std::array<float, 2>& remaining_error) {
    std::lock_guard<std::mutex> lock(mutex_);
    remaining_error_ = remaining_error;
}

// =============================================================================
// 主自瞄系统实现
// =============================================================================

AimingSystem::AimingSystem(const AimingConfig& config) : config_(config) {
    state_ = std::make_unique<AimingState>();

    float sample_time = 1.0f / config_.control_frequency;
    pid_controller_ = std::make_unique<PIDController>(config_.kp, config_.ki, config_.kd, sample_time);

    target_selector_ = std::make_unique<TargetSelector>(config_);
    mouse_controller_ = std::make_unique<MouseController>();
    trigger_system_ = std::make_unique<TriggerSystem>(config_, mouse_controller_.get());
}

AimingSystem::~AimingSystem() {
    Stop();
}

bool AimingSystem::Initialize() {
    std::cout << "[自瞄系统] 初始化完成" << std::endl;
    std::cout << "[自瞄系统] PID参数: Kp=" << config_.kp << ", Ki=" << config_.ki << ", Kd=" << config_.kd << std::endl;
    std::cout << "[自瞄系统] 控制频率: " << config_.control_frequency << "Hz" << std::endl;
    std::cout << "[自瞄系统] 扳机状态: " << (config_.trigger_enabled ? "启用" : "禁用") << std::endl;
    return true;
}

void AimingSystem::Start() {
    if (IsRunning()) {
        return;
    }

    state_->Start();
    control_thread_ = std::make_unique<std::thread>(&AimingSystem::ControlThreadFunc, this);

    std::cout << "[自瞄系统] 已启动" << std::endl;
}

void AimingSystem::Stop() {
    if (!IsRunning()) {
        return;
    }

    state_->RequestStop();

    if (control_thread_ && control_thread_->joinable()) {
        control_thread_->join();
    }

    std::cout << "[自瞄系统] 已停止" << std::endl;
}

bool AimingSystem::IsRunning() const {
    return state_->IsRunning();
}

void AimingSystem::ProcessDetections(const std::vector<Detection>& detections) {
    if (!IsRunning()) {
        return;
    }

    int aim_priority = GetAimPriority();
    bool is_aiming = IsAimKeyPressed();

    state_->UpdateAimStatus(is_aiming);

    if (is_aiming && !detections.empty()) {
        float crosshair_x = config_.screen_center_x;
        float crosshair_y = config_.screen_center_y;

        Target best_target = target_selector_->SelectBestTarget(detections, aim_priority, crosshair_x, crosshair_y);

        if (best_target.class_id >= 0) {
            float offset_y = (best_target.class_id == 0) ? config_.head_offset_y : config_.body_offset_y;
            float target_x = best_target.x;
            float target_y = best_target.y - best_target.height * (0.5f - offset_y);

            std::array<float, 2> total_error = {
                target_x - crosshair_x,
                target_y - crosshair_y
            };

            state_->UpdateTarget(best_target);

            {
                std::lock_guard<std::mutex> lock(state_->mutex_);
                state_->total_error_ = total_error;
            }
        } else {
            state_->ClearTarget();
        }
    } else {
        state_->ClearTarget();
    }
}

void AimingSystem::UpdateConfig(const AimingConfig& config) {
    config_ = config;

    float sample_time = 1.0f / config_.control_frequency;
    pid_controller_->UpdateGains(config_.kp, config_.ki, config_.kd);
    target_selector_->UpdateConfig(config_);
    trigger_system_->UpdateConfig(config_);

    std::cout << "[自瞄系统] 配置已更新" << std::endl;
}

bool AimingSystem::IsAimKeyPressed() {
    return (GetAsyncKeyState(0x02) & 0x8000) != 0;
}

int AimingSystem::GetAimPriority() {
    if (IsAimKeyPressed()) {
        return 1;
    }
    return 0;
}

void AimingSystem::ControlThreadFunc() {
    std::cout << "[控制线程] 启动成功!" << std::endl;

    const float SAMPLE_TIME = 1.0f / config_.control_frequency;
    const auto SAMPLE_DURATION = std::chrono::microseconds(static_cast<int>(SAMPLE_TIME * 1000000));

    std::array<float, 2> remaining_error = {0.0f, 0.0f};

    while (state_->IsRunning()) {
        auto loop_start_time = std::chrono::steady_clock::now();

        auto control_info = state_->GetControlInfo();

        if (!control_info.is_aiming) {
            pid_controller_->Reset();
            remaining_error = {0.0f, 0.0f};
            std::this_thread::sleep_for(SAMPLE_DURATION);
            continue;
        }

        if (control_info.new_target_available) {
            remaining_error = control_info.total_error;
            pid_controller_->Reset();

            std::cout << "[控制线程] 新目标锁定! 总误差: ("
                      << control_info.total_error[0] << ", "
                      << control_info.total_error[1] << ")" << std::endl;
        }

        float error_magnitude = std::sqrt(remaining_error[0] * remaining_error[0] +
                                         remaining_error[1] * remaining_error[1]);

        trigger_system_->CheckAndFire(error_magnitude, control_info.has_valid_target, control_info.is_aiming);

        if (error_magnitude < 0.5f) {
            std::this_thread::sleep_for(SAMPLE_DURATION);
            continue;
        }

        auto pid_output = pid_controller_->Compute(remaining_error[0], remaining_error[1]);

        float move_dx = pid_output[0] * SAMPLE_TIME;
        float move_dy = pid_output[1] * SAMPLE_TIME;

        float max_move = config_.max_move_per_frame;
        if (std::abs(move_dx) > max_move) {
            move_dx = (move_dx > 0) ? max_move : -max_move;
        }
        if (std::abs(move_dy) > max_move) {
            move_dy = (move_dy > 0) ? max_move : -max_move;
        }

        int int_dx = static_cast<int>(std::round(move_dx));
        int int_dy = static_cast<int>(std::round(move_dy));

        if (std::abs(int_dx) > 0 || std::abs(int_dy) > 0) {
            mouse_controller_->MoveMouse(int_dx, int_dy);
        }

        remaining_error[0] -= move_dx;
        remaining_error[1] -= move_dy;

        state_->UpdateRemainingError(remaining_error);

        auto loop_time = std::chrono::steady_clock::now() - loop_start_time;
        auto sleep_duration = SAMPLE_DURATION - loop_time;
        if (sleep_duration > std::chrono::microseconds(0)) {
            std::this_thread::sleep_for(sleep_duration);
        }
    }

    std::cout << "[控制线程] 已停止" << std::endl;
}
