连接盒子 kmNet_init

首先必须调用连接盒子：int kmNet_init(char* ip, char* port, char* uuid);//ok

第一个参数ip	:盒子的IP地址。显示屏上有。
第二个参数port	:盒子的端口号。显示屏上有。
第三个参数UUID	:硬件唯一标识码。显示屏上有。


鼠标相对移动kmNet_mouse_move
int kmNet_mouse_move(short x, short y);
此函数用来控制鼠标移动，耗时约为1ms。此移动是立即移动，不存在中间状态（跳变）。
如下图所示：
鼠标当前在坐标（0,0）点，调用kmNet_mouse_move（100,100）后。鼠标会直接移动到坐标（100,100）。
中间没有任何的过渡。这种移动很容易被游戏检测到键鼠异常。因为正常用物理鼠标操作，从（0,0）移动到（100,100）。会有很多过渡点（过程逼近）。正常鼠标每次移动的最小单位为1.理论上从P0（0,0）点移动到P1(100,100).至少要有100*√2=144个过渡点。如果一次性从（0,0）阶跃到（100,100）那么可能就会被游戏认为鼠标数据异常。因为你省略了中间143个过渡点。kmNet_mouse_move函数用作最基本的移动。适合自己写移动曲线。



鼠标模拟人工移动 kmNet_mouse_move_auto
int kmNet_mouse_move_auto(int x, int y,int time_ms);//ok

与kmNet_mouse_move的不同之处在于第三个参数。kmNet_mouse_move是阶跃的一步到位，速度最快，而 kmNet_mouse_move_auto是自动带中间过程的移动。适用于自动模拟人工轨迹。第三个参数是指定此次移动在多少个毫秒内完成。例如：

kmNet_mouse_auto_move(1920,1080,200);//预设200ms内完成

是移动到（1920,1080）点，要求在200ms内完成。盒子收到此指令后会自动填充中间人工移动轨迹。
在200ms内移动到（1920,1080）的坐标点。


如上图所示。在给定200ms的参数后，盒子经过204ms后将（0,0）到（1920,1080）的中间所有轨迹模拟出来了。此函数适用于没有轨迹模拟的软件调用。盒子可以自动帮助生成中间状态的轨迹。另外第三个参数很重要，一切请以实际人工操作耗时为参考。例如：

kmNet_mouse_auto_move(1920,1080,1);第三个参数给1ms.这一定会有问题。没人能1ms将鼠标指针移动（1920,1080）。第三个参数尽量以人工操作耗时为参考。参数异常导致盒子被检测。不要说我硬件问题谢谢。（为了防止键鼠异常，做硬件的真是操碎了心啊！）。并且每次的移动路径都是随机不一样的。详细可参考demo调用代码



另外后续会加入更多高级算法。鼠标合速度，分速度，加速度，一阶导，二阶导，N阶导，方向向量等。杜绝鼠标数据异常问题。
鼠标按键控制
鼠标按键控制包括以下几个函数：
int kmNet_mouse_left(int isdown);//左键控制ok
int kmNet_mouse_right(int isdown);//右键控制ok
int kmNet_mouse_middle(int isdown);//中键控制ok
int kmNet_mouse_wheel(int wheel);//滚轮控制ok
int kmNet_mouse_all(int button, int x, int y, int wheel);//鼠标全部数据一次性控制ok

Isdown=0时表示抬起，1时表示按下。
Wheel为正时表示下滑滚轮，负时表示上滑滚轮。
Button是鼠标按键，x,y是坐标，wheel是滚轮。


键盘控制类函数

键盘控制类函数主要有以下几个：
int kmNet_keydown(int vkey);// ok
int kmNet_keyup(int vkey);  // ok

其中vkey是按键的HID键码表，键盘按键按下调用kmNet_keydown函数，松开调用kmNet_keyup函数即可。



物理键鼠状态获取
当需要知道盒子上的键盘或者鼠标按键是否按下时可以使用此类函数。这些函数是直接读取盒子硬件。不会调用系统的API，也能在一定程度上防止hook检测。

//监控系列
int kmNet_monitor(short enable); //打开关闭物理键鼠状态监测
int kmNet_monitor_mouse_left(); //鼠标左键是否按下
int kmNet_monitor_mouse_middle();//鼠标中键是否按下
int kmNet_monitor_mouse_right();//鼠标右键是否按下
int kmNet_monitor_mouse_side1();//鼠标侧键1是否按下
int kmNet_monitor_mouse_side2();//鼠标侧键2是否按下
int kmNet_monitor_keyboard(int vk_key);//键盘指定按键vk_key是否按下。

注意，以上函数均为实时状态，即调用时刻检测当前鼠标状态，不会阻塞。不影响与上位机发送其他指令。因为传输指令和接收监控信息是在不同的端口进行。调用监控类函数前请先调用一遍kmNet_monitor（port），使能监控。port为socket的端口号。注意Port不要与本机其他应用程序端口冲突。Port=0时为关闭监听。


物理键鼠屏蔽
//物理键鼠屏蔽系列
int kmNet_mask_mouse_left(int enable);	//屏蔽鼠标左键 
int kmNet_mask_mouse_right(int enable);	//屏蔽鼠标右键 
int kmNet_mask_mouse_middle(int enable);//屏蔽鼠标中键 
int kmNet_mask_mouse_side1(int enable);	//屏蔽鼠标侧键键1 
int kmNet_mask_mouse_side2(int enable);	//屏蔽鼠标侧键键2
int kmNet_mask_mouse_x(int enable);		//屏蔽鼠标X轴坐标
int kmNet_mask_mouse_y(int enable);		//屏蔽鼠标y轴坐标
int kmNet_mask_mouse_wheel(int enable);	//屏蔽鼠标滚轮
int kmNet_mask_keyboard(short vkey);	//屏蔽键盘指定按键
int kmNet_unmask_all();				//解除屏蔽所有已经设置的物理屏蔽


