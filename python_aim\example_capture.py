import cv2
import numpy as np
import time
from infer import ONNXInference
from postprocess import DetectionPostProcessor
from capture import ScreenCapture
from mouse_controller import move_mouse, get_aim_priority
# 导入PIL库处理中文显示
from PIL import Image, ImageDraw, ImageFont

# 导入KMBox控制器
try:
    from kmbox_controller import init_kmbox, move_mouse_kmbox, cleanup as cleanup_kmbox
    KMBOX_AVAILABLE = True
    print("成功加载KMBox控制器!")
except ImportError:
    KMBOX_AVAILABLE = False
    print("未找到KMBox控制器，将仅使用系统移动!")

# 尝试导入自适应PID控制器，如果失败则回退到标准PID控制器
try:
    from adaptive_pid_controller import AdaptivePIDController as PIDController
    print("成功加载自适应PID控制器(带积分分离)!")
except ImportError:
    print("未找到自适应PID控制器，回退使用标准PID控制器...")
    # 尝试导入C++实现的PID控制器，如果失败则回退到Python实现
    try:
        from cpp_pid_controller import PIDController
        print("成功加载C++版PID控制器!")
    except ImportError:
        print("未找到C++版PID控制器，回退使用Python版...")
        from pid_controller import PIDController

# --- 中文文本渲染函数 ---
def cv2_putText_cn(img, text, org, font_path=None, font_size=None, color=(0, 255, 0), thickness=2):
    """使用PIL绘制中文文本，然后转换为OpenCV格式"""
    # 使用全局字体设置，如果未指定
    if font_path is None:
        font_path = FONT_PATH
    if font_size is None:
        font_size = FONT_SIZE
        
    # 创建一个空白的PIL图像，与原始图像大小相同
    pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    
    # 创建字体对象
    try:
        font = ImageFont.truetype(font_path, font_size)
    except IOError:
        # 如果找不到指定字体，尝试使用其他常见中文字体
        alt_fonts = [
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/simkai.ttf",  # 楷体
            "C:/Windows/Fonts/simfang.ttf"  # 仿宋
        ]
        
        font_loaded = False
        for alt_font in alt_fonts:
            try:
                font = ImageFont.truetype(alt_font, font_size)
                print(f"使用替代字体: {alt_font}")
                font_loaded = True
                break
            except IOError:
                continue
                
        if not font_loaded:
            print(f"无法加载任何中文字体，使用默认字体")
            font = ImageFont.load_default()
    
    # 创建绘图对象
    draw = ImageDraw.Draw(pil_img)
    
    # 绘制文本
    draw.text(org, text, fill=(color[2], color[1], color[0]), font=font)
    
    # 将PIL图像转换回OpenCV格式
    return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)


# --- 全局配置 ---
# 移动方式: "system" (系统移动) 或 "kmbox" (KMBox硬件移动)
MOVE_MODE = "kmbox"
# KMBox模拟人类移动的模式: "normal", "human", "beizer"
KMBOX_MOVE_TYPE = "normal"
# KMBox硬件连接参数
KMBOX_IP = "*************"
KMBOX_PORT = "8808"
KMBOX_MAC = "62587019"
# 中文字体设置
FONT_PATH = "C:/Windows/Fonts/simhei.ttf"  # 默认使用黑体
FONT_SIZE = 20                              # 默认字体大小
# 全局PID控制器
pid_controller = None
# PID参数预设
pid_gains = {
    # 系统移动PID参数
    "system": {
        "kp": 1.0,                  # 比例系数
        "ki": 0.0,                  # 积分系数(最大值)
        "kd": 0.005,                # 微分系数
        "base_ki": 0.0,             # 基础积分系数(大误差时)
        "max_ki": 20.0,              # 最大积分系数(小误差时)
        "inner_threshold": 5.0,    # 内阈值，小于此值用最大积分
        "outer_threshold": 30.0     # 外阈值，大于此值禁用积分
    },
    # KMBox移动PID参数
    "kmbox": {
        "kp": 1.0,                  # 比例系数
        "ki": 0.0,                  # 积分系数(最大值)
        "kd": 0.008,                # 微分系数
        "base_ki": 0.0,             # 基础积分系数(大误差时)
        "max_ki": 20.0,              # 最大积分系数(小误差时)
        "inner_threshold": 5.0,    # 内阈值，小于此值用最大积分
        "outer_threshold": 30.0     # 外阈值，大于此值禁用积分
    }
}


# --- 辅助函数：计算到准星的距离 ---
def calculate_distance(box, crosshair_x, crosshair_y):
    """计算目标框中心到准星的距离"""
    target_x = box[0] + box[2] / 2
    target_y = box[1] + box[3] / 2
    return np.sqrt((target_x - crosshair_x)**2 + (target_y - crosshair_y)**2)


# --- 移动鼠标的统一接口 ---
def move_mouse_wrapper(dx, dy):
    """
    根据当前配置选择移动方式
    
    参数:
        dx (int): X方向移动的像素数
        dy (int): Y方向移动的像素数
    """
    global MOVE_MODE
    
    if MOVE_MODE == "kmbox" and KMBOX_AVAILABLE:
        # 使用KMBox硬件移动
        return move_mouse_kmbox(dx, dy, mode=KMBOX_MOVE_TYPE)
    else:
        # 使用系统移动
        return move_mouse(dx, dy)


# --- 切换移动方式的函数 ---
def toggle_move_mode():
    """切换移动方式: 系统移动 <-> KMBox硬件移动"""
    global MOVE_MODE, pid_controller
    
    # 切换前的模式
    old_mode = MOVE_MODE
    
    if MOVE_MODE == "system":
        if KMBOX_AVAILABLE:
            # 在切换到KMBox模式前确保已初始化
            if not init_kmbox(KMBOX_IP, KMBOX_PORT, KMBOX_MAC):
                print("\n切换失败: 无法初始化KMBox硬件")
                return
                
            MOVE_MODE = "kmbox"
            print("\n已切换到KMBox硬件移动")
        else:
            print("\nKMBox不可用，保持使用系统移动")
    else:
        MOVE_MODE = "system"
        print("\n已切换到系统移动")
        
    # 如果模式确实发生了变化，并且PID控制器已初始化，更新PID控制器参数
    if old_mode != MOVE_MODE and pid_controller is not None:
        # 获取新模式的参数
        new_gains = pid_gains[MOVE_MODE]
        
        # 检查是否是自适应PID控制器
        try:
            # 更新自适应PID控制器所有参数
            pid_controller.set_gains(
                new_gains["kp"],
                new_gains["ki"],
                new_gains["kd"],
                new_gains.get("base_ki", 0.0),
                new_gains.get("max_ki", new_gains["ki"])
            )
            
            # 如果控制器有设置阈值的方法，则设置阈值
            if hasattr(pid_controller, 'set_thresholds'):
                pid_controller.set_thresholds(
                    new_gains.get("inner_threshold", 10.0),
                    new_gains.get("outer_threshold", 50.0)
                )
                print(f"已更新自适应PID参数: Kp={new_gains['kp']}, Ki范围=[{new_gains.get('base_ki', 0.0)}, {new_gains.get('max_ki', new_gains['ki'])}], Kd={new_gains['kd']}")
                print(f"积分分离阈值: 内={new_gains.get('inner_threshold', 10.0)}, 外={new_gains.get('outer_threshold', 50.0)}")
            else:
                print(f"已更新PID参数: Kp={new_gains['kp']}, Ki={new_gains['ki']}, Kd={new_gains['kd']}")
        except (TypeError, AttributeError):
            # 更新标准PID控制器参数
            pid_controller.set_gains(
                new_gains["kp"],
                new_gains["ki"],
                new_gains["kd"]
            )
            print(f"已更新PID参数: Kp={new_gains['kp']}, Ki={new_gains['ki']}, Kd={new_gains['kd']}")
        
        # 重置PID状态
        pid_controller.reset()


# --- 【新增】核心自瞄逻辑函数 ---
def get_best_target(detections, aim_priority, crosshair_x, crosshair_y):
    """
    根据按键优先级，从检测结果中筛选出最佳目标。
    
    参数:
        detections (list): postprocessor返回的检测结果列表。
        aim_priority (int): get_aim_priority()返回的优先级 (0, 1, or 2)。
        crosshair_x (int): 准星的X坐标。
        crosshair_y (int): 准星的Y坐标。
        
    返回:
        dict or None: 如果找到目标，返回该目标的字典；否则返回None。
    """
    if aim_priority == 0:
        return None

    # 1. 按类别筛选目标
    targets_class_0 = [d for d in detections if d['class_id'] == 0]
    targets_class_1 = [d for d in detections if d['class_id'] == 1]
    
    # 2. 根据按键，动态设定优先级列表
    if aim_priority == 1:
        primary_targets = targets_class_0
        secondary_targets = targets_class_1
    else: # aim_priority == 2
        primary_targets = targets_class_1
        secondary_targets = targets_class_0
    
    # 3. 优先在主目标列表中寻找
    if primary_targets:
        primary_targets.sort(key=lambda d: calculate_distance(d['box'], crosshair_x, crosshair_y))
        return primary_targets[0]
    
    # 4. 如果主列表为空，则在次要目标列表中寻找
    elif secondary_targets:
        secondary_targets.sort(key=lambda d: calculate_distance(d['box'], crosshair_x, crosshair_y))
        return secondary_targets[0]
        
    return None


def main():
    global MOVE_MODE, pid_controller  # 声明全局变量
    screen_w, screen_h = 1920, 1080
    
    # 如果KMBox可用，初始化KMBox控制器
    if KMBOX_AVAILABLE and MOVE_MODE == "kmbox":
        print(f"正在初始化KMBox控制器 (IP: {KMBOX_IP}, Port: {KMBOX_PORT})...")
        if not init_kmbox(KMBOX_IP, KMBOX_PORT, KMBOX_MAC):
            print("KMBox初始化失败，将使用系统移动")
            MOVE_MODE = "system"
    
    # 指定模型路径，如果有问题可以尝试不同的模型
    model_path = "models/PUBGV8_320.onnx"
    print(f"正在加载模型: {model_path}")
    model = ONNXInference(model_path=model_path)

    # 【新增】创建置顶窗口
    cv2.namedWindow("result", cv2.WINDOW_NORMAL)
    cv2.setWindowProperty("result", cv2.WND_PROP_TOPMOST, 1)
    
    # 获取模型的输入尺寸
    input_shape = model.get_input_size()
    # 我们假设模型是正方形输入，所以取第一个元素作为边长
    input_size = input_shape[0]
    print(f"模型输入尺寸: {input_shape}, 使用 {input_size}x{input_size} 作为截图区域")

    # 截图区域的中心点，也即我们的"准星"位置
    crosshair_x = input_size // 2
    crosshair_y = input_size // 2

    cap_x = (screen_w - input_size) // 2
    cap_y = (screen_h - input_size) // 2
    
    # 计算截图区域
    cap_area = (
        cap_x,
        cap_y,
        input_size,
        input_size
    )
    print(f"截图区域: 左上角({cap_x}, {cap_y}), 尺寸{input_size}x{input_size}")
    
    # 初始化PID控制器 - 根据当前移动方式选择合适的参数
    current_gains = pid_gains[MOVE_MODE]
    try:
        # 尝试使用所有参数初始化(适用于自适应PID控制器)
        pid_controller = PIDController(
            kp=current_gains["kp"], 
            ki=current_gains["ki"], 
            kd=current_gains["kd"],
            base_ki=current_gains.get("base_ki", 0.0),
            max_ki=current_gains.get("max_ki", current_gains["ki"]),
            inner_threshold=current_gains.get("inner_threshold", 10.0),
            outer_threshold=current_gains.get("outer_threshold", 50.0)
        )
        print("已初始化自适应PID控制器")
    except TypeError:
        # 如果失败，使用基本参数初始化(适用于标准PID控制器)
        pid_controller = PIDController(
            kp=current_gains["kp"], 
            ki=current_gains["ki"], 
            kd=current_gains["kd"]
        )
        print("已初始化标准PID控制器")
    
    # 【修改】使用更低的置信度阈值，便于检测到更多目标
    processor = DetectionPostProcessor(confidence_threshold=0.7, nms_threshold=0.1)
    print("初始化完成，开始主循环...")
    print(f"当前移动方式: {MOVE_MODE.upper()}")
    print("按 'M' 键切换移动方式，按 'Esc' 键退出程序")
    
    try:
        with ScreenCapture(roi=cap_area) as cap:
            while True:
                # --- 1. 截图计时 ---
                start_capture = time.perf_counter()
                img = cap.capture()
                end_capture = time.perf_counter()
                capture_time = (end_capture - start_capture) * 1000
                current_time = time.perf_counter()  # 当前时间戳

                # 增加对空帧的检查，增强代码健壮性
                if img is not None:
                    # --- 2. 推理计时 ---
                    start_infer = time.perf_counter()
                    output = model.inference(img)
                    end_infer = time.perf_counter()
                    infer_time = (end_infer - start_infer) * 1000
                    
                    # 【新增】输出模型输出形状，帮助调试
                    #if output is not None:
                       # print(f"\n模型输出形状: {output.shape}")

                    # --- 3. 后处理计时 ---
                    start_post = time.perf_counter()
                    # 传入正确的input_shape
                    result_img, detections = processor.process(output, img, input_shape)
                    end_post = time.perf_counter()
                    post_time = (end_post - start_post) * 1000
                    
                    # 【新增】在画面上显示检测结果数量
                    detection_count = len(detections)
                    # 创建文本图层
                    text_layer = result_img.copy()
                    text_layer = cv2_putText_cn(text_layer, f"检测数量: {detection_count}", (10, 30))
                    
                    # 【新增】显示当前移动方式
                    move_mode_text = f"移动方式: {MOVE_MODE.upper()}"
                    text_layer = cv2_putText_cn(text_layer, move_mode_text, (10, 60))
                    
                    # 如果没有检测到物体，在画面中央显示提示
                    if detection_count == 0:
                        text_layer = cv2_putText_cn(text_layer, "未检测到目标", (input_size//2-100, input_size//2), font_size=30, color=(0, 0, 255))
                        # 画一个准星
                        cv2.line(result_img, (crosshair_x-10, crosshair_y), (crosshair_x+10, crosshair_y), (0, 255, 255), 1)
                        cv2.line(result_img, (crosshair_x, crosshair_y-10), (crosshair_x, crosshair_y+10), (0, 255, 255), 1)

                    # --- 【V7.0 自瞄算法 - 纯PID控制器】 ---
                    # 1. 获取按键优先级
                    aim_priority = get_aim_priority()

                    # 检查按键状态 - 如果没有按下自瞄键，重置PID控制器状态
                    if aim_priority == 0 and pid_controller is not None:
                        pid_controller.reset()
                        
                    # 2. 调用封装好的逻辑函数，获取最终目标
                    final_target = get_best_target(detections, aim_priority, crosshair_x, crosshair_y)
                    
                    # 3. 如果找到了目标，则计算并移动鼠标
                    if final_target:
                        box = final_target['box']
                        
                        # 计算目标中心点（瞄头）
                        target_x = box[0] + box[2] / 2
                        target_y = box[1] + box[3] * 0.13  # 瞄头位置 (头部10%的位置)
                        
                        # 在图像上标记目标点 (红色)
                        cv2.circle(result_img, (int(target_x), int(target_y)), 3, (0, 0, 255), -1)
                        
                        # 计算误差向量 (目标位置 - 准星位置)
                        error = np.array([target_x - crosshair_x, target_y - crosshair_y])
                        
                        # 使用PID控制器计算移动向量
                        pid_output = pid_controller.compute(error, current_time)
                        
                        # 转换为整数移动量
                        move_dx = int(pid_output[0])
                        move_dy = int(pid_output[1])
                        
                        # 在图像上显示PID参数和目标信息
                        gains = pid_controller.get_gains()
                        
                        # 尝试获取当前积分系数(如果是自适应PID控制器)
                        current_ki = "N/A"
                        try:
                            if hasattr(pid_controller, 'get_current_ki'):
                                current_ki = f"{pid_controller.get_current_ki(error):.2f}"
                        except:
                            pass
                            
                        # 显示PID参数
                        pid_info = f"PID: Kp={gains['kp']:.2f}, Ki={current_ki}, Kd={gains['kd']:.4f}"
                        text_layer = cv2_putText_cn(text_layer, pid_info, (10, 90))
                        target_info = f"目标: 类别={final_target['class_name']}, 置信度={final_target['confidence']:.2f}"
                        text_layer = cv2_putText_cn(text_layer, target_info, (10, 120))
                        
                        # 移动鼠标 (使用移动包装函数)
                        move_mouse_wrapper(move_dx, move_dy)
                    # --- 自瞄算法结束 ---

                    # 将文本图层与结果图像合并
                    result_img = text_layer

                    # 显示结果图像
                    cv2.imshow("result", result_img)

                    # --- 打印耗时 ---
                    print(f"\r截图: {capture_time:.2f}ms | 推理: {infer_time:.2f}ms | 后处理: {post_time:.2f}ms | 检测数量: {detection_count} | 移动方式: {MOVE_MODE}", end="")

                # 处理键盘输入
                key = cv2.waitKey(1)
                
                # 使用 Esc 键退出
                if key == 27:
                    break
                    
                # 使用 M 键切换移动方式
                elif key == ord('m') or key == ord('M'):
                    toggle_move_mode()
    except RuntimeError as e:
        print(f"发生运行时错误: {e}")
    
    finally:
        # 清理资源
        if KMBOX_AVAILABLE:
            cleanup_kmbox()
            
        # 【新增】在退出循环后打印一个换行符，避免命令行显示错乱
        print() 
        cv2.destroyAllWindows()
        print("--- 测试程序结束 ---")

if __name__ == "__main__":
    main()