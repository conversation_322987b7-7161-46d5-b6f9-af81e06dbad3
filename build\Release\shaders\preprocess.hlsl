// GPU Preprocessing Compute Shader
// Converts BGRA screen capture to RGB model input with normalization

// Constant buffer for preprocessing parameters
cbuffer PreprocessConstants : register(b0)
{
    float inputWidth;
    float inputHeight;
    float outputWidth;
    float outputHeight;
    float normalizationScale;
    float meanR, meanG, meanB;
    float stdR, stdG, stdB;
    float3 padding;
};

// Input texture (screen capture - BGRA format)
Texture2D<float4> inputTexture : register(t0);

// Output texture (model input - RGB float format)
RWTexture2D<float4> outputTexture : register(u0);

// Bilinear sampling function
float4 SampleBilinear(Texture2D<float4> tex, float2 uv)
{
    // Convert UV to pixel coordinates
    float2 texSize = float2(inputWidth, inputHeight);
    float2 pixelCoord = uv * texSize - 0.5;
    
    // Get integer and fractional parts
    int2 coord0 = int2(floor(pixelCoord));
    int2 coord1 = coord0 + int2(1, 1);
    float2 frac = pixelCoord - floor(pixelCoord);
    
    // Clamp coordinates to texture bounds
    coord0 = clamp(coord0, int2(0, 0), int2(inputWidth - 1, inputHeight - 1));
    coord1 = clamp(coord1, int2(0, 0), int2(inputWidth - 1, inputHeight - 1));
    
    // Sample four neighboring pixels
    float4 sample00 = inputTexture[coord0];
    float4 sample10 = inputTexture[int2(coord1.x, coord0.y)];
    float4 sample01 = inputTexture[int2(coord0.x, coord1.y)];
    float4 sample11 = inputTexture[coord1];
    
    // Bilinear interpolation
    float4 sample0 = lerp(sample00, sample10, frac.x);
    float4 sample1 = lerp(sample01, sample11, frac.x);
    return lerp(sample0, sample1, frac.y);
}

[numthreads(16, 16, 1)]
void CSMain(uint3 id : SV_DispatchThreadID)
{
    // Check bounds
    if (id.x >= outputWidth || id.y >= outputHeight)
        return;
    
    // Calculate UV coordinates for sampling
    float2 uv = (float2(id.xy) + 0.5) / float2(outputWidth, outputHeight);
    
    // Sample input texture with bilinear filtering
    float4 inputColor = SampleBilinear(inputTexture, uv);
    
    // Convert BGRA to RGB (swap B and R channels)
    float3 rgbColor = float3(inputColor.b, inputColor.g, inputColor.r);
    
    // Apply normalization: (pixel / 255.0 - mean) / std
    rgbColor = rgbColor * normalizationScale; // Convert to 0-1 range
    rgbColor.r = (rgbColor.r - meanR) / stdR;
    rgbColor.g = (rgbColor.g - meanG) / stdG;
    rgbColor.b = (rgbColor.b - meanB) / stdB;
    
    // Write to output texture (RGB format, alpha = 1.0)
    outputTexture[id.xy] = float4(rgbColor, 1.0);
}
