import cv2
import numpy as np
import time
import threading
import argparse
from typing import Dict, Any, Optional
import os
import sys
from datetime import datetime
import ctypes
import json

# =============================================================
# ================= 程序全局配置区（从外部文件加载） =================
# =============================================================

# 导入配置加载器
from config_loader import load_config, get_config, update_config, print_config_summary

# 加载配置文件
CONFIG = load_config("config.json")



# =============================================================
# ================= 导入模块和核心功能实现 ====================
# =============================================================

# --- 核心模块导入 ---
from infer import ONNXInference
from postprocess_enhanced import EnhancedDetectionPostProcessor
from capture import ScreenCapture, UDPMjpegCapture
from mouse_controller import move_mouse, get_aim_priority

# --- 新的多线程架构模块导入 ---
from aiming_state import AimingState
from pid_controller_v2 import PIDControllerV2

# 导入PIL库处理中文显示
from PIL import Image, ImageDraw, ImageFont

# 导入KMBox控制器
try:
    from kmbox_controller import init_kmbox, move_mouse_kmbox, cleanup as cleanup_kmbox
    KMBOX_AVAILABLE = True
    print("成功加载KMBox控制器!")
except ImportError:
    KMBOX_AVAILABLE = False
    print("未找到KMBox控制器，将仅使用系统移动!")

# 导入KMBoxNet支持
try:
    import kmNet
    KMBOXNET_AVAILABLE = True
    print("成功加载KMBoxNet模块!")
except ImportError:
    KMBOXNET_AVAILABLE = False
    print("未找到KMBoxNet模块，将使用默认按键监听方式!")

# 导入KMBoxNet支持
try:
    import kmNet
    KMBOXNET_AVAILABLE = True
    print("成功加载KMBoxNet模块!")
except ImportError:
    KMBOXNET_AVAILABLE = False
    print("未找到KMBoxNet模块，将使用默认按键监听方式!")

# --- 全局变量设置，从配置中读取 ---
MOVE_MODE = CONFIG["mouse"]["move_mode"]
KMBOX_MOVE_TYPE = CONFIG["mouse"]["kmbox"]["move_type"]
KMBOX_IP = CONFIG["mouse"]["kmbox"]["ip"]
KMBOX_PORT = CONFIG["mouse"]["kmbox"]["port"]
KMBOX_MAC = CONFIG["mouse"]["kmbox"]["mac"]
FONT_PATH = CONFIG["display"]["font_path"]
FONT_SIZE = CONFIG["display"]["font_size"]
pid_gains = CONFIG["pid"]

# KMBoxNet状态
KMBOXNET_INITIALIZED = False

# --- 同步配置到mouse_controller.py ---
import mouse_controller
# 移除原有的AIM_KEY_PRIO_1/2同步

# 新的获取优先级和偏移的函数

def get_aim_priority_and_offset():
    """
    遍历所有优先级下的按键，只要有一个被按下就返回(优先级编号, offset_y, 按键完整配置)
    """
    import mouse_controller
    def is_key_pressed(key_code):
        if hasattr(mouse_controller, 'kmboxnet_is_key_pressed'):
            return mouse_controller.kmboxnet_is_key_pressed(key_code)
        else:
            return mouse_controller.is_key_pressed(key_code)
    aim_keys = CONFIG["mouse"]["aim_keys"]
    for idx, prio in enumerate(["priority_1", "priority_2"], start=1):
        for key_cfg in aim_keys.get(prio, []):
            if is_key_pressed(key_cfg["key"]):
                return idx, key_cfg["offset_y"], key_cfg
    return 0, 0.5, None  # 默认无优先级，偏移为0.5，无配置

# 初始化KMBoxNet函数
def init_kmboxnet():
    global KMBOXNET_INITIALIZED
    if KMBOXNET_AVAILABLE:
        try:
            kmNet.init(KMBOX_IP, KMBOX_PORT, KMBOX_MAC)
            kmNet.monitor(1)  # 启用按键监听功能
            KMBOXNET_INITIALIZED = True
            print(f"KMBoxNet初始化成功 (IP: {KMBOX_IP}, 端口: {KMBOX_PORT})")
            return True
        except Exception as e:
            print(f"KMBoxNet初始化失败: {e}")
            KMBOXNET_INITIALIZED = False
    return False

# KMBoxNet按键状态检查函数
def kmboxnet_is_key_pressed(key_code):
    if not KMBOXNET_INITIALIZED:
        return False
    
    try:
        if key_code == 0x01:  # 鼠标左键
            return kmNet.isdown_left()
        elif key_code == 0x02:  # 鼠标右键
            return kmNet.isdown_right()
        elif key_code == 0x05:  # 鼠标下侧键
            return kmNet.isdown_side1()
        elif key_code == 0x06:  # 鼠标上侧键
            return kmNet.isdown_side2()
        # 其他按键目前不支持
        return False
    except Exception as e:
        print(f"KMBoxNet按键检查失败: {e}")
        return False

# 将原函数替换为新函数的引用
if KMBOXNET_AVAILABLE:
    # 替换原始get_aim_priority函数
    mouse_controller.original_get_aim_priority = mouse_controller.get_aim_priority
    # 不需要替换函数，使用原始函数即可

# --- 辅助函数 ---
def cv2_putText_cn(img, text, org, font_path=None, font_size=None, color=(0, 255, 0), thickness=2):
    """在图像上绘制中文文本"""
    if font_path is None: font_path = FONT_PATH
    if font_size is None: font_size = FONT_SIZE
    try:
        font = ImageFont.truetype(font_path, font_size)
    except IOError:
        alt_fonts = ["C:/Windows/Fonts/msyh.ttc", "C:/Windows/Fonts/simsun.ttc"]
        font_loaded = False
        for alt_font in alt_fonts:
            try:
                font = ImageFont.truetype(alt_font, font_size)
                font_loaded = True
                break
            except IOError: continue
        if not font_loaded: font = ImageFont.load_default()
    pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_img)
    draw.text(org, text, fill=(color[2], color[1], color[0]), font=font)
    return cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)

# move_mouse_wrapper中集成ghub

def move_mouse_wrapper(dx, dy):
    if MOVE_MODE == "kmbox":
        if KMBOXNET_INITIALIZED:
            try:
                # 使用KMBoxNet移动鼠标
                kmNet.move(int(dx), int(dy))
                return True
            except Exception as e:
                print(f"KMBoxNet移动鼠标失败: {e}")
                return False
        elif KMBOX_AVAILABLE:
            # 使用原始KMBox移动方式
            return move_mouse_kmbox(dx, dy, mode=KMBOX_MOVE_TYPE)
    elif MOVE_MODE == "ghub":
        from mouse_controller import move_mouse_ghub
        return move_mouse_ghub(dx, dy)
    # 默认使用系统移动方式
    return move_mouse(dx, dy)

def calculate_distance(box, crosshair_x, crosshair_y):
    target_x = box[0] + box[2] / 2
    target_y = box[1] + box[3] / 2
    return np.sqrt((target_x - crosshair_x)**2 + (target_y - crosshair_y)**2)

def get_best_target(detections, aim_priority, crosshair_x, crosshair_y):
    if aim_priority == 0: return None
    targets_class_0 = [d for d in detections if d['class_id'] == 0]
    targets_class_1 = [d for d in detections if d['class_id'] == 1]
    primary_targets = targets_class_0 if aim_priority == 1 else targets_class_1
    secondary_targets = targets_class_1 if aim_priority == 1 else targets_class_0
    if primary_targets:
        primary_targets.sort(key=lambda d: calculate_distance(d['box'], crosshair_x, crosshair_y))
        return primary_targets[0]
    elif secondary_targets:
        secondary_targets.sort(key=lambda d: calculate_distance(d['box'], crosshair_x, crosshair_y))
        return secondary_targets[0]
    return None

# --- 命令行参数解析 ---
def parse_arguments() -> Dict[str, Any]:
    parser = argparse.ArgumentParser(description='AI辅助瞄准程序')
    parser.add_argument('--model', type=str, help='模型路径')
    parser.add_argument('--model-type', choices=['yolov5', 'yolov8'], help='模型类型: yolov5 或 yolov8')
    parser.add_argument('--capture', choices=['screen', 'udp'], help='截图方式: screen(屏幕) 或 udp(OBS流)')
    parser.add_argument('--move', choices=['system', 'kmbox'], help='移动方式: system(系统API) 或 kmbox(硬件)')
    parser.add_argument('--num-classes', type=int, help='模型类别数量')
    args = parser.parse_args()
    
    # 用命令行参数更新配置
    config_update = {}
    if args.model:
        config_update["model"] = {"path": args.model}
    if args.model_type:
        if "model" not in config_update:
            config_update["model"] = {}
        config_update["model"]["model_type"] = args.model_type
    if args.num_classes:
        if "model" not in config_update:
            config_update["model"] = {}
        config_update["model"]["num_classes"] = args.num_classes
    if args.capture:
        config_update["capture"] = {"method": args.capture}
    if args.move:
        config_update["mouse"] = {"move_mode": args.move}
    
    return config_update

# --- 更新配置 ---
def update_config(config_updates: Dict[str, Any]):
    """根据提供的字典更新全局CONFIG"""
    global CONFIG, MOVE_MODE, KMBOX_MOVE_TYPE, KMBOX_IP, KMBOX_PORT, KMBOX_MAC
    
    # 递归更新嵌套字典
    def update_nested_dict(d, u):
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                update_nested_dict(d[k], v)
            else:
                d[k] = v
    
    # 更新配置
    for section, values in config_updates.items():
        if section in CONFIG:
            if isinstance(values, dict) and isinstance(CONFIG[section], dict):
                update_nested_dict(CONFIG[section], values)
            else:
                CONFIG[section] = values
    
    # 更新全局变量
    MOVE_MODE = CONFIG["mouse"]["move_mode"]
    KMBOX_MOVE_TYPE = CONFIG["mouse"]["kmbox"]["move_type"]
    KMBOX_IP = CONFIG["mouse"]["kmbox"]["ip"]
    KMBOX_PORT = CONFIG["mouse"]["kmbox"]["port"]
    KMBOX_MAC = CONFIG["mouse"]["kmbox"]["mac"]
    
    # 同步配置到mouse_controller
    # 移除原有的AIM_KEY_PRIO_1/2同步

# --- ========================================================= ---
# ---              新架构：控制线程 (Control Thread)              ---
# --- ========================================================= ---
import threading
from mouse_controller import mouse_left_down_ghub, mouse_left_up_ghub

def mouse_left_down_system():
    """使用Windows API按下鼠标左键"""
    try:
        import win32api
        import win32con
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        return True
    except Exception as e:
        print(f"[System] 鼠标左键按下失败: {e}")
        return False

def mouse_left_up_system():
    """使用Windows API松开鼠标左键"""
    try:
        import win32api
        import win32con
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        return True
    except Exception as e:
        print(f"[System] 鼠标左键松开失败: {e}")
        return False

def trigger_mouse_click():
    print(f"[扳机] trigger_mouse_click called, MOVE_MODE={MOVE_MODE}")
    if MOVE_MODE == "ghub":
        print("[扳机] 使用ghub点击")
        mouse_left_down_ghub()
        time.sleep(0.03)
        mouse_left_up_ghub()
    elif MOVE_MODE == "kmbox":
        print("[扳机] 使用kmbox点击")
        from mouse_controller import mouse_left_down_kmbox, mouse_left_up_kmbox
        mouse_left_down_kmbox()
        time.sleep(0.03)
        mouse_left_up_kmbox()
    else:
        print("[扳机] 使用system点击")
        mouse_left_down_system()
        time.sleep(0.03)
        mouse_left_up_system()

def control_thread_func(state: AimingState, pid_controller: PIDControllerV2):
    """
    一个高频运行的独立线程，负责PID计算和鼠标移动。
    它实现了"误差债务"模型。
    """
    print("[控制线程] 启动成功!")
    
    # --- 控制循环参数 ---
    # 固定的时间步长，决定了控制的平滑度
    SAMPLE_TIME = 1.0 / CONFIG["pid"]["control_frequency"]  # 从配置中读取控制频率
    
    # 内部状态：剩余需要修正的误差
    remaining_error = np.array([0.0, 0.0])
    # --- 已移除：用于调试打印的计数器 ---
    # debug_counter = 0
    last_fire_time = 0
    trigger_lock = threading.Lock()
    trigger_fired = [False]  # 用列表包裹以便闭包修改

    def try_trigger_fire(has_valid_target=False):
        """
        尝试触发扳机
        参数:
            has_valid_target: 是否有有效目标被锁定
        """
        aim_priority, offset_y, key_cfg = get_aim_priority_and_offset()
        print(f"[扳机] 检测到aim_priority={aim_priority}, has_valid_target={has_valid_target}, key_cfg={key_cfg}")

        # 检查是否启用扳机
        if not key_cfg or not key_cfg.get("trigger", {}).get("enabled", False):
            print("[扳机] 当前按键未启用trigger")
            return

        # 检查是否有有效目标 - 这是关键的安全检查！
        if not has_valid_target:
            print("[扳机] 没有有效目标，不开火")
            return

        now = time.time() * 1000  # ms
        fire_interval = key_cfg["trigger"].get("fire_interval_ms", 120)
        fire_delay = key_cfg["trigger"].get("fire_delay_ms", 50)
        with trigger_lock:
            if now - try_trigger_fire.last_fire < fire_interval:
                print(f"[扳机] 距离上次开枪间隔{now - try_trigger_fire.last_fire:.1f}ms < {fire_interval}ms，跳过")
                return
            try_trigger_fire.last_fire = now
        def fire():
            print(f"[扳机] fire线程启动，延迟{fire_delay}ms后点击")
            time.sleep(fire_delay / 1000)
            trigger_mouse_click()
            print("[扳机] fire线程点击完成")
            # 重置trigger_fired标志，允许下次触发
            with trigger_lock:
                trigger_fired[0] = False
        threading.Thread(target=fire, daemon=True).start()
    try_trigger_fire.last_fire = 0

    while state.is_running():
        loop_start_time = time.perf_counter()

        # 1. 从状态管理器获取最新的指令
        is_aiming, total_error, _, new_target_available = state.get_control_info()
        
        if not is_aiming:
            # 如果没有按下自瞄键，重置一切
            pid_controller.reset()
            remaining_error = np.array([0.0, 0.0])
            trigger_fired[0] = False
            # --- 已移除：打印非瞄准状态 ---
            # if debug_counter % 100 == 0:
            #      print(f"\r[CTRL] 状态: 未瞄准 | 等待指令...{' '*50}", end="")
            # debug_counter += 1
            # 休眠以避免CPU空转
            time.sleep(SAMPLE_TIME)
            continue

        # 2. "误差债务"模型核心逻辑
        if new_target_available:
            # 老板发来了新任务！更新我们的总债务
            remaining_error = total_error
            # 切换目标时重置PID积分项，防止旧的积分影响新的移动
            pid_controller.reset()
            # 新目标时不重置trigger_fired，让扳机能够持续工作
            # --- 已移除：打印新目标信息，并换行 ---
            # print(f"\n[CTRL] 新目标锁定! 总误差: ({total_error[0]:.1f}, {total_error[1]:.1f})")


        # 检查是否应该触发扳机（在瞄准过程中）
        error_magnitude = np.linalg.norm(remaining_error)
        # 检查是否有有效目标
        has_valid_target = state.has_valid_target()
        # 只有在有目标且误差在合理范围内时才尝试触发扳机
        if error_magnitude < 30.0 and has_valid_target and is_aiming:  # 当误差小于30像素且有有效目标且正在瞄准时尝试触发扳机
            try_trigger_fire(has_valid_target=True)
        # 如果按下瞄准键但没有目标，偶尔提示一下（避免刷屏）
        elif not has_valid_target and is_aiming:
            # 每100次循环提示一次，避免刷屏
            if hasattr(try_trigger_fire, 'no_target_counter'):
                try_trigger_fire.no_target_counter += 1
            else:
                try_trigger_fire.no_target_counter = 1
            if try_trigger_fire.no_target_counter % 100 == 0:
                print("[扳机] 按下瞄准键但没有有效目标，不触发扳机")

        # 如果没有剩余误差需要修正，则跳过移动
        if error_magnitude < 0.5: # 小于0.5像素的误差忽略不计
            # --- 已移除：打印误差修正完毕状态 ---
            # if debug_counter % 50 == 0:
            #     print(f"\r[CTRL] 状态: 误差已修正 | rem_err: ({remaining_error[0]:.2f}, {remaining_error[1]:.2f}){' '*30}", end="")
            # debug_counter += 1
            time.sleep(SAMPLE_TIME)
            continue
            
        # 3. PID计算
        # 注意：我们将 remaining_error 同时作为 error 和 measurement 传入
        # 这是一种简化，但在我们的场景下是有效的。
        pid_output = pid_controller.compute(remaining_error, remaining_error)

        # 4. 计算本次循环的移动量
        # 我们需要限制单次移动的最大值，防止移动过快
        move_dx = pid_output[0] * SAMPLE_TIME
        move_dy = pid_output[1] * SAMPLE_TIME
        
        # 实际移动并转换为整数
        int_dx = int(round(move_dx))
        int_dy = int(round(move_dy))

        # --- 已移除：核心调试打印 ---
        # print(f"\r[CTRL] rem_err:({remaining_error[0]:.1f}, {remaining_error[1]:.1f}) | pid_out:({pid_output[0]:.1f}, {pid_output[1]:.1f}) | move:({int_dx}, {int_dy}){' '*10}", end="")

        # 5. 执行移动
        if abs(int_dx) > 0 or abs(int_dy) > 0:
            move_mouse_wrapper(int_dx, int_dy)
        
        # 6. 更新剩余误差 (还债)
        remaining_error -= np.array([move_dx, move_dy])

        # 7. 精准休眠，维持固定的循环频率
        loop_time = time.perf_counter() - loop_start_time
        sleep_duration = SAMPLE_TIME - loop_time
        if sleep_duration > 0:
            time.sleep(sleep_duration)
            
    print("[控制线程] 已停止。")


# --- ========================================================= ---
# ---              新架构：视觉线程 (Vision Thread)               ---
# --- ========================================================= ---
def vision_thread_func(state: AimingState, model_path: str = None):
    """
    一个低频运行的独立线程，负责截图、推理和发布"总任务量"。
    """
    print("[视觉线程] 启动成功!")
    global MOVE_MODE # 允许修改全局变量
    
    # --- 初始化 ---
    # 使用配置中的值或传入的参数
    if model_path is None:
        model_path = CONFIG["model"]["path"]
    
    screen_w, screen_h = CONFIG["capture"]["screen_resolution"]
    capture_method = CONFIG["capture"]["method"]
    
    print(f"[视觉线程] 正在加载模型: {model_path}")
    model = ONNXInference(model_path=model_path)
    
    if CONFIG["display"]["show_preview"]:
        cv2.namedWindow("result", cv2.WINDOW_NORMAL)
        cv2.setWindowProperty("result", cv2.WND_PROP_TOPMOST, 1)
    
    input_shape = model.get_input_size()
    input_size = input_shape[0]
    crosshair_x, crosshair_y = input_size // 2, input_size // 2
    aim_circle_radius = CONFIG.get("aim_circle_radius", 180)
    
    cap_x = (screen_w - input_size) // 2
    cap_y = (screen_h - input_size) // 2
    cap_area = (cap_x, cap_y, input_size, input_size)
    
    # 使用增强版后处理器，自动探测模型格式
    print("[视觉线程] 正在初始化增强版后处理器...")
    processor = EnhancedDetectionPostProcessor(
        confidence_threshold=CONFIG["model"]["confidence_threshold"],
        nms_threshold=CONFIG["model"]["nms_threshold"],
        model_path=model_path,     # 提供模型路径进行自动探测
        force_model_type=CONFIG["model"]["model_type"],  # 强制指定模型类型
        num_classes=CONFIG["model"]["num_classes"],      # 指定类别数量
        class_names=CONFIG["model"]["class_names"]       # 指定类别名称
    )
    
    print("[视觉线程] 初始化完成，开始主循环")
    
    # --- 根据配置选择截图方式 ---
    if capture_method == "udp":
        print(f"[视觉线程] 使用UDP视频流作为输入源 ({CONFIG['capture']['udp']['host']}:{CONFIG['capture']['udp']['port']})")
        capturer = UDPMjpegCapture(
            host=CONFIG['capture']['udp']['host'],
            port=CONFIG['capture']['udp']['port'],
            buffer_size=CONFIG['capture']['udp']['buffer_size']
        )
    else:  # 默认使用屏幕截图
        print(f"[视觉线程] 使用屏幕截图作为输入源 (ROI={cap_area})")
        capturer = ScreenCapture(roi=cap_area)
    
    # --- 主循环 ---
    try:
        with capturer:
            while state.is_running():
                # 1. 截图和推理
                t0 = time.perf_counter()
                img = capturer.capture()
                t1 = time.perf_counter()
                if img is None:
                    continue
                # 预处理（如有，可在此插入）
                # t1 = time.perf_counter() # 截图后
                output = model.inference(img)
                t2 = time.perf_counter()
                # 使用增强版后处理器，应用用户指定的模型类型和参数
                result_img, detections = processor.process(
                    output, 
                    img, 
                    input_shape,
                    force_model_type=CONFIG["model"]["model_type"],
                    num_classes=CONFIG["model"]["num_classes"],
                    class_names=CONFIG["model"]["class_names"]
                )
                t3 = time.perf_counter()
                # 调试信息：每步耗时
                if CONFIG.get("debug", {}).get("show_timing", False):
                    capture_time = (t1 - t0) * 1000
                    infer_time = (t2 - t1) * 1000
                    post_time = (t3 - t2) * 1000
                    print(f"\r[调试] 截图: {capture_time:.1f} ms | 推理: {infer_time:.1f} ms | 后处理: {post_time:.1f} ms", end="", flush=True)

                # 2. 获取自瞄按键状态和偏移
                aim_priority, offset_y, key_cfg = get_aim_priority_and_offset()
                
                # 3. 寻找最佳目标（只选圆环内的目标）
                def is_in_aim_circle(box):
                    cx = box[0] + box[2] / 2
                    cy = box[1] + box[3] / 2
                    return np.sqrt((cx - crosshair_x) ** 2 + (cy - crosshair_y) ** 2) <= aim_circle_radius
                filtered_detections = [d for d in detections if is_in_aim_circle(d['box'])]
                final_target = get_best_target(filtered_detections, aim_priority, crosshair_x, crosshair_y)
                
                # 4. 发布任务给状态管理器
                is_aiming_key_pressed = (aim_priority > 0)
                state.update_aim_status(is_aiming_key_pressed)

                if is_aiming_key_pressed and final_target is not None:
                    box = final_target['box']
                    target_x = box[0] + box[2] / 2
                    target_y = box[1] + box[3] * offset_y
                    total_error = np.array([target_x - crosshair_x, target_y - crosshair_y])
                    state.update_target(total_error, np.array([0.0, 0.0]))
                else:
                    # 如果没有按键或没有目标，清除目标信息
                    if not is_aiming_key_pressed or final_target is None:
                        state.clear_target()
                
                # --- 5. 绘制画面 (与控制逻辑无关，仅用于调试显示) ---
                if CONFIG["display"]["show_preview"]:
                    # 在画面中心画圆环
                    cv2.circle(result_img, (crosshair_x, crosshair_y), aim_circle_radius, (0, 255, 255), 2)
                    
                    # 在最终目标上画一个点
                    if final_target:
                        box = final_target['box']
                        target_x = box[0] + box[2] / 2
                        target_y = box[1] + box[3] * 0.13
                        cv2.circle(result_img, (int(target_x), int(target_y)), 3, (0, 0, 255), -1)
                    
                    # --- 新增：在画面上显示详细的调试状态 ---
                    debug_text_y = 30
                    # 移动方式
                    move_mode_text = f"移动方式: {MOVE_MODE.upper()}"
                    result_img = cv2_putText_cn(result_img, move_mode_text, (10, debug_text_y))
                    debug_text_y += 30
                    # 按键状态
                    aim_key_status = "未按"
                    if aim_priority == 1: aim_key_status = "按下(优先0)"
                    if aim_priority == 2: aim_key_status = "按下(优先1)"
                    result_img = cv2_putText_cn(result_img, f"自瞄按键: {aim_key_status}", (10, debug_text_y))
                    debug_text_y += 30
                    # 检测数量
                    result_img = cv2_putText_cn(result_img, f"检测数量: {len(detections)}", (10, debug_text_y))
                    debug_text_y += 30
                    # 最终瞄准状态
                    is_aiming_status = (aim_priority > 0 and final_target is not None)
                    aiming_status_text = "是" if is_aiming_status else "否"
                    aiming_status_color = (0, 255, 0) if is_aiming_status else (0, 0, 255)
                    result_img = cv2_putText_cn(result_img, f"最终瞄准: {aiming_status_text}", (10, debug_text_y), color=aiming_status_color)
                    # 显示扳机状态
                    debug_text_y += 30
                    trigger_enabled = False
                    if aim_priority > 0:
                        _, _, key_cfg = get_aim_priority_and_offset()
                        if key_cfg and key_cfg.get("trigger", {}).get("enabled", False):
                            trigger_enabled = True
                    trigger_status_text = "启用" if trigger_enabled else "禁用"
                    trigger_status_color = (0, 255, 0) if trigger_enabled else (0, 0, 255)
                    result_img = cv2_putText_cn(result_img, f"扳机状态: {trigger_status_text}", (10, debug_text_y), color=trigger_status_color)
                    # 显示截图方式
                    debug_text_y += 30
                    capture_method_text = f"截图方式: {CONFIG['capture']['method'].upper()}"
                    result_img = cv2_putText_cn(result_img, capture_method_text, (10, debug_text_y))

                    cv2.imshow("result", result_img)

                    # 6. 处理键盘输入
                    key = cv2.waitKey(1)
                    if key == 27: # Esc
                        state.request_stop()
                        break

    except Exception as e:
        print(f"[视觉线程] 发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        state.request_stop()
    finally:
        cv2.destroyAllWindows()
        print("[视觉线程] 已停止。")


# --- ========================================================= ---
# ---                      主函数 (Main)                        ---
# --- ========================================================= ---
def main():
    """程序主入口"""
    # 解析命令行参数并更新配置
    config_updates = parse_arguments()
    if config_updates:
        update_config(config_updates)

    print(f"--- 多线程自瞄程序启动 ---")
    print(f"配置文件: config.json")

    # 显示详细的配置摘要
    print_config_summary()
    
    # 1. 初始化鼠标控制
    from mouse_controller import KMBOXNET_AVAILABLE
    
    if MOVE_MODE == "kmbox":
        # 优先尝试初始化KMBoxNet
        if KMBOXNET_AVAILABLE:
            if init_kmboxnet():
                print("已使用KMBoxNet初始化鼠标控制，可接收主机键盘鼠标事件")
            else:
                # 备选：初始化普通KMBox
                if KMBOX_AVAILABLE and init_kmbox(KMBOX_IP, KMBOX_PORT, KMBOX_MAC):
                    print("已使用KMBox初始化鼠标控制")
                else:
                    print("KMBox初始化失败，将使用系统移动")
                    globals()['MOVE_MODE'] = "system" # 修改全局变量
                    CONFIG["mouse"]["move_mode"] = "system"
        elif KMBOX_AVAILABLE:
            # 尝试初始化普通KMBox
            if init_kmbox(KMBOX_IP, KMBOX_PORT, KMBOX_MAC):
                print("已使用KMBox初始化鼠标控制")
            else:
                print("KMBox初始化失败，将使用系统移动")
                globals()['MOVE_MODE'] = "system" # 修改全局变量
                CONFIG["mouse"]["move_mode"] = "system"
    elif MOVE_MODE == "ghub":
        from mouse_controller import move_mouse_ghub
        print("已使用GHub初始化鼠标控制")

    # 2. 初始化线程间通信的状态管理器
    shared_state = AimingState()
    
    # 3. 初始化为新架构设计的PID控制器
    current_gains = pid_gains[MOVE_MODE]
    control_frequency = CONFIG["pid"]["control_frequency"]
    sample_time = 1.0 / control_frequency
    
    pid_controller = PIDControllerV2(
        kp=current_gains["kp"], 
        ki=current_gains["ki"], 
        kd=current_gains["kd"],
        sample_time=sample_time, # 从配置中读取
        base_ki=current_gains.get("base_ki", 0.0),
        max_ki=current_gains.get("max_ki", current_gains["ki"]),
        inner_threshold=current_gains.get("inner_threshold", 10.0),
        outer_threshold=current_gains.get("outer_threshold", 50.0)
    )
    print(f"已初始化新版PID控制器 (V2)，控制频率: {control_frequency}Hz")

    # 4. 创建并启动线程
    vision_thread = threading.Thread(target=vision_thread_func, args=(shared_state, CONFIG["model"]["path"]))
    control_thread = threading.Thread(target=control_thread_func, args=(shared_state, pid_controller))
    
    vision_thread.start()
    control_thread.start()
    
    # 5. 等待线程结束
    vision_thread.join()
    control_thread.join()
    
    # 6. 清理资源
    if KMBOX_AVAILABLE:
        cleanup_kmbox()
    
    print("\n--- 程序已完全退出 ---")


if __name__ == "__main__":
    main() 