# udp_mjpeg_receiver.py
import socket
import cv2
import numpy as np
import threading
import time

class UDPMjpegReceiver:
    def __init__(self, host='127.0.0.1', port=8000, buffer_size=65536):
        """
        初始化UDP MJPEG接收器
        
        Args:
            host: 监听地址，默认为127.0.0.1
            port: 监听端口，默认为8000
            buffer_size: UDP缓冲区大小，默认为65536字节
        """
        self.host = host
        self.port = port
        self.buffer_size = buffer_size
        self.socket = None
        self.running = False
        self.current_frame = None
        self.lock = threading.Lock()
        
        # MJPEG帧缓冲
        self.frame_buffer = bytearray()
        self.jpg_start = bytes([0xFF, 0xD8])  # JPEG起始标记
        self.jpg_end = bytes([0xFF, 0xD9])    # JPEG结束标记
    
    def start(self):
        """启动接收器"""
        if self.running:
            print("接收器已经在运行")
            return
            
        self.running = True
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.socket.bind((self.host, self.port))
        self.socket.settimeout(1.0)  # 设置超时，使stop方法能正常工作
        
        # 启动接收线程
        self.receiver_thread = threading.Thread(target=self._receive_loop)
        self.receiver_thread.daemon = True
        self.receiver_thread.start()
        
        print(f"UDP MJPEG接收器已启动，正在监听 {self.host}:{self.port}")
    
    def stop(self):
        """停止接收器"""
        self.running = False
        if self.receiver_thread:
            self.receiver_thread.join(timeout=2.0)
        if self.socket:
            self.socket.close()
            self.socket = None
        print("UDP MJPEG接收器已停止")
    
    def _receive_loop(self):
        """接收循环，在独立线程中运行"""
        while self.running:
            try:
                data, _ = self.socket.recvfrom(self.buffer_size)
                if data:
                    self._process_data(data)
            except socket.timeout:
                continue
            except Exception as e:
                print(f"接收数据出错: {e}")
    
    def _process_data(self, data):
        """处理接收到的UDP数据包"""
        self.frame_buffer.extend(data)
        
        # 查找完整的JPEG帧
        start_pos = self.frame_buffer.find(self.jpg_start)
        if start_pos == -1:
            return  # 没找到JPEG开始标记
        
        # 从开始标记位置查找结束标记
        end_pos = self.frame_buffer.find(self.jpg_end, start_pos)
        if end_pos == -1:
            return  # 没找到JPEG结束标记
            
        # 提取完整的JPEG帧
        end_pos += 2  # 包含结束标记
        jpg_data = self.frame_buffer[start_pos:end_pos]
        
        # 解码JPEG数据
        try:
            img = cv2.imdecode(np.frombuffer(jpg_data, dtype=np.uint8), cv2.IMREAD_COLOR)
            if img is not None:
                with self.lock:
                    self.current_frame = img
        except Exception as e:
            print(f"解码JPEG数据失败: {e}")
        
        # 清理已处理的数据
        self.frame_buffer = self.frame_buffer[end_pos:]
    
    def get_frame(self):
        """获取当前帧"""
        with self.lock:
            if self.current_frame is None:
                return None
            return self.current_frame.copy()

# 测试代码
if __name__ == "__main__":
    receiver = UDPMjpegReceiver(host='127.0.0.1', port=8000)
    receiver.start()
    
    try:
        cv2.namedWindow("UDP MJPEG Stream", cv2.WINDOW_NORMAL)
        print("等待接收视频流...")
        
        no_frame_count = 0
        last_frame_time = time.time()
        fps = 0
        
        while True:
            frame = receiver.get_frame()
            
            if frame is not None:
                # 计算FPS
                current_time = time.time()
                if current_time - last_frame_time > 0:
                    fps = 1.0 / (current_time - last_frame_time)
                last_frame_time = current_time
                
                # 显示FPS
                cv2.putText(frame, f"FPS: {fps:.2f}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.imshow("UDP MJPEG Stream", frame)
                no_frame_count = 0
            else:
                no_frame_count += 1
                if no_frame_count > 100:  # 如果长时间没有帧，显示提示
                    print("未接收到视频帧，请确认OBS是否正在发送...")
                    no_frame_count = 0
            
            # 按ESC键退出
            if cv2.waitKey(1) & 0xFF == 27:
                break
    
    finally:
        receiver.stop()
        cv2.destroyAllWindows()