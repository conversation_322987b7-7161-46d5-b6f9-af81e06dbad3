#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扳机修复的脚本
验证扳机只在有目标时才触发
"""

import time
import threading
import numpy as np
from aiming_state import AimingState

# 模拟扳机功能测试
def test_trigger_logic():
    """测试扳机逻辑"""
    print("=== 扳机逻辑测试 ===")
    
    # 创建状态管理器
    state = AimingState()
    
    # 测试1: 没有目标时不应该触发扳机
    print("\n测试1: 没有目标时")
    state.update_aim_status(True)  # 按下瞄准键
    has_target = state.has_valid_target()
    print(f"瞄准键状态: 按下")
    print(f"有效目标: {has_target}")
    print(f"应该触发扳机: {has_target}")  # 应该是False
    
    # 测试2: 有目标时应该可以触发扳机
    print("\n测试2: 有目标时")
    state.update_target(np.array([10.0, 15.0]), np.array([0.0, 0.0]))  # 设置目标
    has_target = state.has_valid_target()
    print(f"瞄准键状态: 按下")
    print(f"有效目标: {has_target}")
    print(f"应该触发扳机: {has_target}")  # 应该是True
    
    # 测试3: 目标丢失后不应该触发扳机
    print("\n测试3: 目标丢失后")
    state.clear_target()  # 清除目标
    has_target = state.has_valid_target()
    print(f"瞄准键状态: 按下")
    print(f"有效目标: {has_target}")
    print(f"应该触发扳机: {has_target}")  # 应该是False
    
    # 测试4: 松开瞄准键后不应该触发扳机
    print("\n测试4: 松开瞄准键后")
    state.update_aim_status(False)  # 松开瞄准键
    has_target = state.has_valid_target()
    print(f"瞄准键状态: 松开")
    print(f"有效目标: {has_target}")
    print(f"应该触发扳机: False")  # 应该是False
    
    print("\n=== 测试完成 ===")

def simulate_trigger_conditions():
    """模拟各种扳机触发条件"""
    print("\n=== 扳机触发条件模拟 ===")
    
    # 模拟配置
    trigger_config = {
        "enabled": True,
        "fire_delay_ms": 50,
        "fire_interval_ms": 120
    }
    
    conditions = [
        {"name": "无目标+无按键", "has_target": False, "is_aiming": False, "error": 5.0},
        {"name": "无目标+有按键", "has_target": False, "is_aiming": True, "error": 5.0},
        {"name": "有目标+无按键", "has_target": True, "is_aiming": False, "error": 5.0},
        {"name": "有目标+有按键+误差大", "has_target": True, "is_aiming": True, "error": 50.0},
        {"name": "有目标+有按键+误差小", "has_target": True, "is_aiming": True, "error": 15.0},
    ]
    
    for condition in conditions:
        should_fire = (
            condition["has_target"] and 
            condition["is_aiming"] and 
            condition["error"] < 30.0 and
            trigger_config["enabled"]
        )
        
        print(f"\n条件: {condition['name']}")
        print(f"  有目标: {condition['has_target']}")
        print(f"  瞄准中: {condition['is_aiming']}")
        print(f"  误差: {condition['error']:.1f}像素")
        print(f"  扳机启用: {trigger_config['enabled']}")
        print(f"  → 应该开火: {should_fire}")

if __name__ == "__main__":
    test_trigger_logic()
    simulate_trigger_conditions()
    
    print("\n=== 修复总结 ===")
    print("1. 扳机现在只在以下条件全部满足时才触发:")
    print("   - 按下了启用扳机的瞄准键")
    print("   - 检测到有效目标")
    print("   - 瞄准误差在合理范围内(< 30像素)")
    print("   - 距离上次开火间隔足够长")
    print("2. 修复了之前'按键就开火'的问题")
    print("3. 添加了详细的调试信息")
