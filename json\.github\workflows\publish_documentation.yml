name: Publish documentation

# publish the documentation on every merge to develop branch
on:
  push:
    branches:
      - develop
    paths:
      - docs/mkdocs/**
      - docs/examples/**
  workflow_dispatch:

# we don't want to have concurrent jobs, and we don't want to cancel running jobs to avoid broken publications
concurrency:
  group: documentation
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  publish_documentation:
    permissions:
      contents: write

    if: github.repository == 'nlohmann/json'
    runs-on: ubuntu-22.04
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@6c439dc8bdf85cadbbce9ed30d1c7b959517bc49 # v2.12.2
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Install virtual environment
        run: make install_venv -C docs/mkdocs

      - name: Build documentation
        run: make build -C docs/mkdocs

      - name: Deploy documentation
        uses: peaceiris/actions-gh-pages@4f9cc6602d3f66b9c108549d475ec49e8ef4d45e # v4.0.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/mkdocs/site
