// Format conversion shader: R32G32B32A32_FLOAT to R8G8B8A8_UNORM
// Converts preprocessed float texture to displayable format

struct VSOutput
{
    float4 position : SV_POSITION;
    float2 texCoord : TEXCOORD0;
};

// Vertex shader for fullscreen triangle
VSOutput VSMain(uint vertexID : SV_VertexID)
{
    VSOutput output;
    
    // Generate fullscreen triangle
    output.texCoord = float2((vertexID << 1) & 2, vertexID & 2);
    output.position = float4(output.texCoord * 2.0f - 1.0f, 0.0f, 1.0f);
    output.position.y = -output.position.y; // Flip Y for D3D11
    
    return output;
}

// Source texture (preprocessed float texture)
Texture2D<float4> sourceTexture : register(t0);
SamplerState sourceSampler : register(s0);

// Pixel shader to extract center region and convert format
float4 PSMain(VSOutput input) : SV_TARGET
{
    // Calculate the center region coordinates
    // Assume source is 1920x1080, we want center 320x320
    float2 sourceSize = float2(1920.0f, 1080.0f);
    float2 targetSize = float2(320.0f, 320.0f);

    // Calculate center region in normalized coordinates
    float2 centerOffset = (sourceSize - targetSize) * 0.5f / sourceSize;
    float2 regionSize = targetSize / sourceSize;

    // Map current texture coordinate to center region
    float2 centerUV = centerOffset + input.texCoord * regionSize;

    // Sample the original texture
    float4 originalColor = sourceTexture.Sample(sourceSampler, centerUV);

    // Return original color without channel swapping
    // (DirectX may handle the BGRA format conversion automatically)
    return originalColor;
}
