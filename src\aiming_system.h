#pragma once

#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <array>

// 检测结果结构体（与推理模块接口）
struct Detection {
    float x, y, width, height;  // 边界框坐标和尺寸
    int class_id;               // 类别ID (0=头部, 1=身体)
    float confidence;           // 置信度
};

// 目标信息结构体
struct Target {
    float x, y;                 // 目标中心点坐标
    float width, height;        // 目标尺寸
    int class_id;               // 类别ID
    float confidence;           // 置信度
    std::chrono::steady_clock::time_point timestamp; // 时间戳
    
    Target() : x(0), y(0), width(0), height(0), class_id(-1), confidence(0) {
        timestamp = std::chrono::steady_clock::now();
    }
    
    Target(const Detection& det) 
        : x(det.x + det.width/2), y(det.y + det.height/2)
        , width(det.width), height(det.height)
        , class_id(det.class_id), confidence(det.confidence) {
        timestamp = std::chrono::steady_clock::now();
    }
};

// 自瞄配置结构体
struct AimingConfig {
    // 目标选择配置
    float aim_circle_radius = 180.0f;       // 自瞄圆环半径
    int primary_class = 0;                  // 主要目标类别 (0=头部)
    int secondary_class = 1;                // 次要目标类别 (1=身体)
    float head_offset_y = 0.13f;            // 头部瞄准偏移
    float body_offset_y = 0.5f;             // 身体瞄准偏移
    
    // PID控制参数
    float kp = 2.5f;                        // 比例系数
    float ki = 0.1f;                        // 积分系数
    float kd = 0.3f;                        // 微分系数
    float control_frequency = 200.0f;       // 控制频率 (Hz)
    float max_move_per_frame = 50.0f;       // 单帧最大移动距离
    
    // 扳机配置
    bool trigger_enabled = false;           // 是否启用自动扳机
    float trigger_threshold = 30.0f;        // 扳机触发阈值 (像素)
    int trigger_delay_ms = 50;              // 扳机延迟 (毫秒)
    int fire_interval_ms = 120;             // 开火间隔 (毫秒)
    
    // 屏幕配置
    int screen_center_x = 320;              // 屏幕中心X (对应320x320推理区域)
    int screen_center_y = 320;              // 屏幕中心Y
};

// PID控制器类
class PIDController {
private:
    float kp_, ki_, kd_;
    float sample_time_;
    float integral_;
    float last_error_x_, last_error_y_;
    std::chrono::steady_clock::time_point last_time_;
    
public:
    PIDController(float kp, float ki, float kd, float sample_time);
    
    // 计算PID输出
    std::array<float, 2> Compute(float error_x, float error_y);
    
    // 重置PID状态
    void Reset();
    
    // 更新PID参数
    void UpdateGains(float kp, float ki, float kd);
};

// 目标选择器类
class TargetSelector {
private:
    AimingConfig config_;
    
public:
    TargetSelector(const AimingConfig& config) : config_(config) {}
    
    // 从检测结果中选择最佳目标
    Target SelectBestTarget(const std::vector<Detection>& detections, 
                           int aim_priority, 
                           float crosshair_x, float crosshair_y);
    
    // 计算目标距离
    float CalculateDistance(const Target& target, float crosshair_x, float crosshair_y);
    
    // 检查目标是否在自瞄圆环内
    bool IsInAimCircle(const Target& target, float crosshair_x, float crosshair_y);
    
    // 更新配置
    void UpdateConfig(const AimingConfig& config) { config_ = config; }
};

// 鼠标控制器类
class MouseController {
public:
    // 移动鼠标
    bool MoveMouse(int dx, int dy);
    
    // 鼠标点击
    bool LeftClick();
    bool LeftDown();
    bool LeftUp();
};

// 扳机系统类
class TriggerSystem {
private:
    AimingConfig config_;
    std::chrono::steady_clock::time_point last_fire_time_;
    std::atomic<bool> trigger_active_;
    MouseController* mouse_controller_;
    
public:
    TriggerSystem(const AimingConfig& config, MouseController* mouse_controller);
    
    // 检查并触发扳机
    void CheckAndFire(float error_magnitude, bool has_valid_target, bool is_aiming);
    
    // 更新配置
    void UpdateConfig(const AimingConfig& config) { config_ = config; }
    
private:
    // 异步开火
    void FireAsync();
};

// 自瞄状态管理类
class AimingState {
public:
    mutable std::mutex mutex_;
    
    // 目标状态
    Target current_target_;
    bool has_target_;
    bool target_updated_;
    
    // 控制状态
    bool is_aiming_;
    std::array<float, 2> total_error_;
    std::array<float, 2> remaining_error_;
    
    // 系统状态
    std::atomic<bool> running_;

public:
    AimingState();

    // 更新目标信息
    void UpdateTarget(const Target& target);
    void ClearTarget();

    // 更新瞄准状态
    void UpdateAimStatus(bool is_aiming);
    
    // 获取控制信息
    struct ControlInfo {
        bool is_aiming;
        std::array<float, 2> total_error;
        std::array<float, 2> remaining_error;
        bool new_target_available;
        bool has_valid_target;
    };
    ControlInfo GetControlInfo();
    
    // 更新剩余误差
    void UpdateRemainingError(const std::array<float, 2>& remaining_error);
    
    // 系统控制
    bool IsRunning() const { return running_.load(); }
    void RequestStop() { running_.store(false); }
    void Start() { running_.store(true); }
};

// 主自瞄系统类
class AimingSystem {
private:
    AimingConfig config_;
    
    // 核心组件
    std::unique_ptr<AimingState> state_;
    std::unique_ptr<PIDController> pid_controller_;
    std::unique_ptr<TargetSelector> target_selector_;
    std::unique_ptr<MouseController> mouse_controller_;
    std::unique_ptr<TriggerSystem> trigger_system_;
    
    // 线程
    std::unique_ptr<std::thread> control_thread_;
    
    // 按键状态检查
    bool IsAimKeyPressed();
    int GetAimPriority();
    
public:
    AimingSystem(const AimingConfig& config = AimingConfig{});
    ~AimingSystem();
    
    // 系统控制
    bool Initialize();
    void Start();
    void Stop();
    bool IsRunning() const;
    
    // 主要接口：处理检测结果
    void ProcessDetections(const std::vector<Detection>& detections);
    
    // 配置管理
    void UpdateConfig(const AimingConfig& config);
    AimingConfig GetConfig() const { return config_; }
    
private:
    // 控制线程函数
    void ControlThreadFunc();
};
