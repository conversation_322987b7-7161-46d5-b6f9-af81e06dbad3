// Detection box drawing shader
// This shader draws bounding boxes and labels on top of the source image

// Constant buffer for detection data - must match C++ struct layout
cbuffer DetectionBuffer : register(b0)
{
    float4 detections[64];  // Each detection: (x, y, width, height) - 64*16 = 1024 bytes
    float4 colors[64];      // Each color: (r, g, b, confidence) - 64*16 = 1024 bytes
    uint detectionCount;    // 4 bytes
    float imageWidth;       // 4 bytes
    float imageHeight;      // 4 bytes
    float lineWidth;        // 4 bytes
    // Total: 2048 + 16 = 2064 bytes
};

// Vertex shader for fullscreen quad
struct VSOutput
{
    float4 position : SV_POSITION;
    float2 texCoord : TEXCOORD0;
};

VSOutput VSMain(uint vertexID : SV_VertexID)
{
    VSOutput output;
    
    // Generate fullscreen triangle
    output.texCoord = float2((vertexID << 1) & 2, vertexID & 2);
    output.position = float4(output.texCoord * 2.0f - 1.0f, 0.0f, 1.0f);
    output.position.y = -output.position.y; // Flip Y for D3D11
    
    return output;
}

// Pixel shader for drawing boxes
Texture2D sourceTexture : register(t0);
SamplerState sourceSampler : register(s0);

float4 PSMain(VSOutput input) : SV_TARGET
{
    // Sample the original image
    float4 originalColor = sourceTexture.Sample(sourceSampler, input.texCoord);

    // Early return if no detections - just show crosshair
    if (detectionCount == 0) {
        // Draw simple crosshair in center
        float2 center = float2(0.5f, 0.5f); // Normalized center
        float2 diff = abs(input.texCoord - center);

        if ((diff.x < 0.03f && diff.y < 0.003f) || (diff.y < 0.03f && diff.x < 0.003f)) {
            return float4(lerp(originalColor.rgb, float3(0.0f, 1.0f, 0.0f), 0.7f), 1.0f);
        }
        return originalColor;
    }

    // Convert texture coordinates to pixel coordinates
    float2 pixelPos = input.texCoord * float2(imageWidth, imageHeight);

    // Check detections - only draw on borders
    for (uint i = 0; i < min(detectionCount, 64u); ++i)
    {
        float4 detection = detections[i];
        float4 color = colors[i];

        // Skip invalid detections
        if (detection.z <= 0.0f || detection.w <= 0.0f) continue;

        // Convert normalized coordinates to pixel coordinates
        float centerX = detection.x * imageWidth;
        float centerY = detection.y * imageHeight;
        float boxWidth = detection.z * imageWidth;
        float boxHeight = detection.w * imageHeight;

        // Calculate box boundaries
        float left = centerX - boxWidth * 0.5f;
        float right = centerX + boxWidth * 0.5f;
        float top = centerY - boxHeight * 0.5f;
        float bottom = centerY + boxHeight * 0.5f;

        // Ensure box is within image bounds
        if (left < 0 || right > imageWidth || top < 0 || bottom > imageHeight) continue;
        if (boxWidth < 10.0f || boxHeight < 10.0f) continue; // Skip tiny boxes

        // Check if pixel is on box border (2 pixel thick border)
        float borderThickness = 2.0f;
        bool onLeftEdge = (pixelPos.x >= left - borderThickness && pixelPos.x <= left + borderThickness) &&
                         (pixelPos.y >= top && pixelPos.y <= bottom);
        bool onRightEdge = (pixelPos.x >= right - borderThickness && pixelPos.x <= right + borderThickness) &&
                          (pixelPos.y >= top && pixelPos.y <= bottom);
        bool onTopEdge = (pixelPos.y >= top - borderThickness && pixelPos.y <= top + borderThickness) &&
                        (pixelPos.x >= left && pixelPos.x <= right);
        bool onBottomEdge = (pixelPos.y >= bottom - borderThickness && pixelPos.y <= bottom + borderThickness) &&
                           (pixelPos.x >= left && pixelPos.x <= right);

        if (onLeftEdge || onRightEdge || onTopEdge || onBottomEdge)
        {
            // Draw bright colored border that stands out
            return float4(color.rgb, 1.0f);
        }
    }

    // Return transparent if not on any border (for blending)
    return float4(0.0f, 0.0f, 0.0f, 0.0f);
}
