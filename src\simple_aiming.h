#pragma once

#include <vector>
#include <windows.h>

// 检测结果结构体（与推理模块接口）
struct Detection {
    float x, y, width, height;  // 边界框坐标和尺寸
    int class_id;               // 类别ID (0=头部, 1=身体)
    float confidence;           // 置信度
};

// 简化的自瞄配置
struct SimpleAimConfig {
    float aim_circle_radius = 180.0f;   // 自瞄圆环半径
    int primary_class = 0;              // 主要目标类别 (0=头部)
    int secondary_class = 1;            // 次要目标类别 (1=身体)
    float head_offset_y = 0.13f;        // 头部瞄准偏移
    float body_offset_y = 0.5f;         // 身体瞄准偏移
    int screen_center_x = 160;          // 屏幕中心X (320x320区域的中心)
    int screen_center_y = 160;          // 屏幕中心Y
    bool enabled = false;               // 是否启用自瞄
};

// 简化的自瞄系统类
class SimpleAiming {
private:
    SimpleAimConfig config_;
    
public:
    SimpleAiming(const SimpleAimConfig& config = SimpleAimConfig{});
    
    // 处理检测结果并移动鼠标
    void ProcessDetections(const std::vector<Detection>& detections);
    
    // 配置管理
    void UpdateConfig(const SimpleAimConfig& config) { config_ = config; }
    SimpleAimConfig GetConfig() const { return config_; }
    
    // 启用/禁用自瞄
    void SetEnabled(bool enabled) { config_.enabled = enabled; }
    bool IsEnabled() const { return config_.enabled; }
    
private:
    // 检查按键状态
    bool IsAimKeyPressed();
    
    // 从检测结果中选择最佳目标
    Detection SelectBestTarget(const std::vector<Detection>& detections);
    
    // 计算目标距离
    float CalculateDistance(const Detection& detection, float crosshair_x, float crosshair_y);
    
    // 检查目标是否在自瞄圆环内
    bool IsInAimCircle(const Detection& detection, float crosshair_x, float crosshair_y);
    
    // 移动鼠标到目标位置
    void MoveMouseToTarget(const Detection& target);
    
    // 系统鼠标移动
    bool MoveMouse(int dx, int dy);
};
