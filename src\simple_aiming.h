#pragma once

#include <vector>
#include <windows.h>

// 妫€娴嬬粨鏋滅粨鏋勪綋锛堜笌鎺ㄧ悊妯″潡鎺ュ彛锛�
struct Detection {
    float x, y, width, height;  // 杈圭晫妗嗗潗鏍囧拰灏哄
    int class_id;               // 绫诲埆ID (0=澶撮儴, 1=韬綋)
    float confidence;           // 缃俊搴�
};

// 绠€鍖栫殑鑷瀯閰嶇疆
struct SimpleAimConfig {
    float aim_circle_radius = 180.0f;   // 鑷瀯鍦嗙幆鍗婂緞
    int primary_class = 0;              // 涓昏鐩爣绫诲埆 (0=澶撮儴)
    int secondary_class = 1;            // 娆¤鐩爣绫诲埆 (1=韬綋)
    float head_offset_y = 0.13f;        // 澶撮儴鐬勫噯鍋忕Щ
    float body_offset_y = 0.5f;         // 韬綋鐬勫噯鍋忕Щ
    int screen_center_x = 160;          // 灞忓箷涓績X (320x320鍖哄煙鐨勪腑蹇�)
    int screen_center_y = 160;          // 灞忓箷涓績Y
    bool enabled = false;               // 鏄惁鍚敤鑷瀯
};

// 绠€鍖栫殑鑷瀯绯荤粺绫�
class SimpleAiming {
private:
    SimpleAimConfig config_;
    
public:
    SimpleAiming(const SimpleAimConfig& config = SimpleAimConfig{});
    
    // 澶勭悊妫€娴嬬粨鏋滃苟绉诲姩榧犳爣
    void ProcessDetections(const std::vector<Detection>& detections);
    
    // 閰嶇疆绠＄悊
    void UpdateConfig(const SimpleAimConfig& config) { config_ = config; }
    SimpleAimConfig GetConfig() const { return config_; }
    
    // 鍚敤/绂佺敤鑷瀯
    void SetEnabled(bool enabled) { config_.enabled = enabled; }
    bool IsEnabled() const { return config_.enabled; }
    
private:
    // 妫€鏌ユ寜閿姸鎬�
    bool IsAimKeyPressed();
    
    // 浠庢娴嬬粨鏋滀腑閫夋嫨鏈€浣崇洰鏍�
    Detection SelectBestTarget(const std::vector<Detection>& detections);
    
    // 璁＄畻鐩爣璺濈
    float CalculateDistance(const Detection& detection, float crosshair_x, float crosshair_y);
    
    // 妫€鏌ョ洰鏍囨槸鍚﹀湪鑷瀯鍦嗙幆鍐�
    bool IsInAimCircle(const Detection& detection, float crosshair_x, float crosshair_y);
    
    // 绉诲姩榧犳爣鍒扮洰鏍囦綅缃�
    void MoveMouseToTarget(const Detection& target);
    
    // 绯荤粺榧犳爣绉诲姩
    bool MoveMouse(int dx, int dy);
};
