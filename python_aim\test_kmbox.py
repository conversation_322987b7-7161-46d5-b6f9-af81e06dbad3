import os
import sys
import time

# 自动添加bin目录到sys.path和DLL搜索路径
bin_path = os.path.join(os.path.dirname(__file__), 'bin')
if bin_path not in sys.path:
    sys.path.append(bin_path)
if hasattr(os, 'add_dll_directory'):
    os.add_dll_directory(bin_path)

try:
    import kmNet
    print("kmNet模块导入成功！")
except ImportError as e:
    print(f"kmNet模块导入失败: {e}")
    sys.exit(1)

# KMBox参数（可根据实际情况修改）
KMBOX_IP = "*************"
KMBOX_PORT = "8808"
KMBOX_MAC = "62587019"

print(f"尝试初始化KMBox: IP={KMBOX_IP}, PORT={KMBOX_PORT}, MAC={KMBOX_MAC}")
try:
    result = kmNet.init(KMBOX_IP, KMBOX_PORT, KMBOX_MAC)
    if result == 0:
        print("KMBox初始化成功！")
        print("尝试移动鼠标 (10, 10)...")
        move_result = kmNet.move(10, 10)
        print(f"移动结果: {move_result}")
        print("测试完成。")
    else:
        print(f"KMBox初始化失败，错误码: {result}")
except Exception as e:
    print(f"KMBox操作异常: {e}") 