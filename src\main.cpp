#include <iostream>
#include <iomanip>
#include <windows.h>
#include <d3d11.h>
#include <wrl/client.h>
#include <cmath>
#include <cfloat>

// Core project headers
#include "screen_capture.h"
#include "gpu_preprocessor.h"
#include "gpu_renderer.h"
#include "inference_engine.h"
#include "kmbox_controller.h"
#include "pid_controller.h"
// #include "simple_aiming.h"  // 暂时禁用

using Microsoft::WRL::ComPtr;

// Simple screen capture test - capture, preprocess and display
bool RunSimpleScreenCapture() {
    std::cout << "=== Simple Screen Capture Test ===" << std::endl;
    std::cout << "Testing: Screen Capture -> GPU Preprocessing -> Display" << std::endl;
    std::cout << "Press ESC to exit" << std::endl;

    // Initialize screen capture
    std::cout << "Initializing screen capture..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "Failed to initialize screen capture" << std::endl;
        return false;
    }
    std::cout << "Screen capture initialized successfully" << std::endl;

    // Initialize GPU preprocessor with center 320x320 ROI
    std::cout << "Initializing GPU preprocessor..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;   // Screen resolution
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);  // Center 320x320 region
    params.normalizationScale = 1.0f;  // No scaling, keep original colors
    params.meanR = 0.0f;  // No mean subtraction
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;   // No standard deviation scaling
    params.stdG = 1.0f;
    params.stdB = 1.0f;
    
    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "Failed to initialize GPU preprocessor" << std::endl;
        return false;
    }
    
    // Print ROI info
    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU preprocessor initialized successfully" << std::endl;
    std::cout << "ROI: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // Initialize GPU renderer for format conversion
    std::cout << "Initializing GPU renderer..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = false;
    renderConfig.showBoundingBoxes = false;
    
    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize GPU renderer" << std::endl;
        return false;
    }
    std::cout << "GPU renderer initialized successfully" << std::endl;

    // Initialize GPU display window
    std::cout << "Initializing GPU display window..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize display window" << std::endl;
        return false;
    }
    std::cout << "Display window initialized successfully" << std::endl;

    std::cout << "\nStarting simple screen capture..." << std::endl;
    std::cout << "A window will show the center 320x320 region of your screen" << std::endl;
    std::cout << "Format: [Frame] Capture:Xms Preprocess:Xms Render:Xms Total:Xms | FPS: X" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    while (!displayWindow.ShouldClose()) {
        // Process window messages
        displayWindow.ProcessMessages();

        frameCount++;

        // High precision timing
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. Capture screen
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. Preprocess (extract center 320x320 region and convert BGRA to RGB)
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. Convert format and render (R32G32B32A32_FLOAT -> R8G8B8A8_UNORM)
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        // No detections for simple display, just format conversion
        if (!renderer.RenderDetections(preprocessedTexture.Get(), nullptr, 0, renderedTexture)) {
            continue;
        }
        
        // 4. Display the rendered texture
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + displayTime;

        // Show performance info every 30 frames
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);
            
            std::cout << "[" << frameCount << "] "
                      << "Capture:" << std::fixed << std::setprecision(2) << captureTime << "ms "
                      << "Preprocess:" << preprocessTime << "ms "
                      << "Render:" << displayTime << "ms "
                      << "Total:" << totalTime << "ms | "
                      << "FPS: " << std::setprecision(1) << fps << std::endl;
        }

        // ESC key check
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    std::cout << "Simple screen capture test completed!" << std::endl;
    return true;
}

// Real-time inference with display
bool RunRealtimeInference() {
    std::cout << "=== Real-time Inference Test ===" << std::endl;
    std::cout << "Testing: Screen Capture -> GPU Preprocessing -> AI Inference -> Display" << std::endl;
    std::cout << "Press ESC to exit" << std::endl;

    // Initialize screen capture
    std::cout << "Initializing screen capture..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "Failed to initialize screen capture" << std::endl;
        return false;
    }
    std::cout << "Screen capture initialized successfully" << std::endl;

    // Initialize GPU preprocessor with center 320x320 ROI
    std::cout << "Initializing GPU preprocessor..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;   // Screen resolution
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);  // Center 320x320 region
    params.normalizationScale = 1.0f;  // No scaling for now
    params.meanR = 0.0f;  // No mean subtraction
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;   // No standard deviation scaling
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "Failed to initialize GPU preprocessor" << std::endl;
        return false;
    }

    // Print ROI info
    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU preprocessor initialized successfully" << std::endl;
    std::cout << "ROI: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // Initialize inference engine
    std::cout << "Initializing inference engine..." << std::endl;
    InferenceEngine inferenceEngine;
    InferenceConfig inferenceConfig;
    inferenceConfig.modelPath = "models/PUBGV8_320.onnx";
    inferenceConfig.confidenceThreshold = 0.5f;
    inferenceConfig.useGPU = true;

    if (!inferenceEngine.Initialize(capture.GetDevice().Get(), inferenceConfig)) {
        std::cout << "Failed to initialize inference engine" << std::endl;
        return false;
    }
    std::cout << "Inference engine initialized successfully" << std::endl;

    // Initialize GPU renderer for format conversion and detection display
    std::cout << "Initializing GPU renderer..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = true;   // Show crosshair
    renderConfig.showBoundingBoxes = true;  // Show detection boxes

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize GPU renderer" << std::endl;
        return false;
    }
    std::cout << "GPU renderer initialized successfully" << std::endl;

    // Initialize KMBox hardware mouse controller
    std::cout << "Initializing KMBox hardware mouse controller..." << std::endl;
    KMBoxConfig kmboxConfig;
    kmboxConfig.ip = "*************";           // 修改为你的KMBox IP地址
    kmboxConfig.port = "8808";                 // 修改为你的KMBox端口
    kmboxConfig.mac = "62587019";                  // 修改为你的KMBox MAC地址
    kmboxConfig.use_smooth_move = true;        // 启用平滑移动
    kmboxConfig.smooth_move_time_ms = 30;      // 30ms平滑移动时间
    kmboxConfig.use_encryption = false;        // 先使用非加密版本测试
    kmboxConfig.enabled = true;                // 启用KMBox控制

    KMBoxController kmboxController(kmboxConfig);

    // 尝试连接KMBox (如果连接失败，程序仍然可以运行，只是使用系统鼠标)
    bool kmboxConnected = kmboxController.Initialize();
    if (kmboxConnected) {
        std::cout << "KMBox hardware mouse controller connected successfully!" << std::endl;
        // 测试连接
        if (kmboxController.TestConnection()) {
            std::cout << "KMBox connection test passed!" << std::endl;
        }
    } else {
        std::cout << "KMBox connection failed - will use system mouse as fallback" << std::endl;
        std::cout << "Please check:" << std::endl;
        std::cout << "  1. KMBox IP address: " << kmboxConfig.ip << std::endl;
        std::cout << "  2. KMBox port: " << kmboxConfig.port << std::endl;
        std::cout << "  3. KMBox MAC: " << kmboxConfig.mac << std::endl;
        std::cout << "  4. Network connection to KMBox" << std::endl;
        std::cout << "  5. Windows firewall settings" << std::endl;
        kmboxController.SetEnabled(false);
    }

    // Initialize PID controller for smooth aiming
    std::cout << "正在初始化PID控制器..." << std::endl;
    PIDConfig pidConfig;
    pidConfig.kp = 3.0f;                        // 比例增益 (响应速度)
    pidConfig.ki = 0.15f;                       // 积分增益 (消除稳态误差)
    pidConfig.kd = 1.2f;                        // 微分增益 (减少超调)
    pidConfig.max_output = 40.0f;               // 最大输出限制
    pidConfig.min_output = 0.8f;                // 最小输出阈值
    pidConfig.integral_limit = 80.0f;           // 积分限幅
    pidConfig.adaptive_enabled = true;          // 启用自适应PID
    pidConfig.distance_threshold = 120.0f;      // 距离阈值
    pidConfig.close_kp_multiplier = 0.5f;       // 近距离Kp倍数
    pidConfig.far_kp_multiplier = 1.3f;         // 远距离Kp倍数
    pidConfig.smoothing_factor = 0.75f;         // 输出平滑因子
    pidConfig.velocity_prediction = true;       // 启用速度预测
    pidConfig.control_frequency = 200.0f;       // 控制频率

    PIDController pidController(pidConfig);
    std::cout << "PID控制器初始化完成！" << std::endl;

    // Initialize GPU display window
    std::cout << "Initializing GPU display window..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize display window" << std::endl;
        return false;
    }
    std::cout << "Display window initialized successfully" << std::endl;

    std::cout << "\n开始实时推理..." << std::endl;
    std::cout << "窗口将显示屏幕中心320x320区域的AI检测结果" << std::endl;
    std::cout << "窗口控制:" << std::endl;
    std::cout << "  - F1键: 切换窗口置顶 (当前: 开启)" << std::endl;
    std::cout << "  - ESC键: 退出程序" << std::endl;
    std::cout << "自瞄控制:" << std::endl;
    std::cout << "  - 鼠标右键: 激活平滑自瞄" << std::endl;
    std::cout << "硬件鼠标:" << std::endl;
    std::cout << "  - KMBox状态: " << (kmboxConnected ? "已连接" : "未连接") << std::endl;
    std::cout << "  - 鼠标控制: " << (kmboxConnected ? "硬件控制 (KMBox)" : "系统控制 (备用)") << std::endl;
    std::cout << "格式: [帧数] 截图:Xms 预处理:Xms 推理:Xms 渲染:Xms 总计:Xms | FPS: X | 检测数: X" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    while (!displayWindow.ShouldClose()) {
        // Process window messages
        displayWindow.ProcessMessages();

        frameCount++;

        // High precision timing
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. Capture screen
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. Preprocess (extract center 320x320 region and convert BGRA to RGB)
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. Run AI inference
        QueryPerformanceCounter(&start);
        std::vector<Detection> detections;
        if (!inferenceEngine.RunInference(preprocessedTexture.Get(), detections)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double inferenceTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 4. TODO: Process detections with simple aiming system (temporarily disabled)
        // simpleAiming.ProcessDetections(detections);

        // PID控制器自瞄系统
        bool aimKeyPressed = (GetAsyncKeyState(0x02) & 0x8000) != 0;

        // 如果连接了KMBox，也可以使用KMBox的按键监控作为备选
        if (kmboxConnected && kmboxController.IsEnabled()) {
            // 使用KMBox硬件按键检测作为备选 (可选功能)
            // aimKeyPressed = aimKeyPressed || kmboxController.IsRightButtonPressed();
        }

        if (aimKeyPressed && !detections.empty()) {
            std::cout << "[PID自瞄] 发现 " << detections.size() << " 个目标，右键已按下" << std::endl;

            // Target selection parameters
            const float SCREEN_CENTER_X = 160.0f;  // Center of 320x320 region
            const float SCREEN_CENTER_Y = 160.0f;
            const float AIM_CIRCLE_RADIUS = 180.0f;
            const int PRIMARY_CLASS = 0;  // Head priority
            const int SECONDARY_CLASS = 1; // Body secondary

            // Find targets within aim circle
            Detection* bestTarget = nullptr;
            float minDistance = FLT_MAX;

            // First pass: look for primary targets (heads)
            for (auto& det : detections) {
                float centerX = det.x + det.width / 2.0f;
                float centerY = det.y + det.height / 2.0f;

                // Calculate distance from crosshair
                float dx = centerX - SCREEN_CENTER_X;
                float dy = centerY - SCREEN_CENTER_Y;
                float distance = sqrt(dx * dx + dy * dy);

                // Check if target is within aim circle and is primary class
                if (distance <= AIM_CIRCLE_RADIUS && det.classId == PRIMARY_CLASS && distance < minDistance) {
                    bestTarget = &det;
                    minDistance = distance;
                }
            }

            // Second pass: if no primary target found, look for secondary targets
            if (!bestTarget) {
                minDistance = FLT_MAX;
                for (auto& det : detections) {
                    float centerX = det.x + det.width / 2.0f;
                    float centerY = det.y + det.height / 2.0f;

                    float dx = centerX - SCREEN_CENTER_X;
                    float dy = centerY - SCREEN_CENTER_Y;
                    float distance = sqrt(dx * dx + dy * dy);

                    if (distance <= AIM_CIRCLE_RADIUS && det.classId == SECONDARY_CLASS && distance < minDistance) {
                        bestTarget = &det;
                        minDistance = distance;
                    }
                }
            }

            // If we found a target, move mouse to it
            if (bestTarget) {
                float targetX = bestTarget->x + bestTarget->width / 2.0f;
                float targetY = bestTarget->y + bestTarget->height / 2.0f;

                // Adjust aim point based on target class
                if (bestTarget->classId == 0) {
                    // Head: aim slightly higher
                    targetY = bestTarget->y + bestTarget->height * 0.13f;
                } else {
                    // Body: aim at center
                    targetY = bestTarget->y + bestTarget->height * 0.5f;
                }

                // Step 3: Smooth movement algorithm
                float deltaX = targetX - SCREEN_CENTER_X;
                float deltaY = targetY - SCREEN_CENTER_Y;

                // Smooth movement parameters
                const float SMOOTH_FACTOR = 0.3f;  // How much of the distance to move (0.1 = very smooth, 1.0 = instant)
                const float MIN_MOVE_THRESHOLD = 1.0f;  // Minimum movement to avoid micro-movements

                // Apply smoothing
                float smoothMoveX = deltaX * SMOOTH_FACTOR;
                float smoothMoveY = deltaY * SMOOTH_FACTOR;

                // Only move if the movement is significant enough
                if (abs(smoothMoveX) > MIN_MOVE_THRESHOLD || abs(smoothMoveY) > MIN_MOVE_THRESHOLD) {
                    int moveX = static_cast<int>(round(smoothMoveX));
                    int moveY = static_cast<int>(round(smoothMoveY));

                    bool moveSuccess = false;
                    std::string moveMethod = "SYSTEM";

                    // 优先使用KMBox硬件鼠标控制
                    if (kmboxConnected && kmboxController.IsEnabled()) {
                        if (kmboxController.MoveMouseAuto(moveX, moveY)) {
                            moveSuccess = true;
                            moveMethod = "KMBOX";
                        } else {
                            std::cout << "[KMBox] Hardware move failed, falling back to system mouse" << std::endl;
                        }
                    }

                    // 使用系统鼠标移动
                    POINT currentPos;
                    if (GetCursorPos(&currentPos)) {
                        if (SetCursorPos(currentPos.x + moveX, currentPos.y + moveY)) {
                            moveSuccess = true;
                            moveMethod = "SYSTEM";
                        }
                    }

                    if (moveSuccess) {
                        std::cout << "[" << moveMethod << " 自瞄] 移动到 " << (bestTarget->classId == 0 ? "头部" : "身体")
                                  << " 目标，平滑移动 (" << moveX << ", " << moveY << ")" << std::endl;
                    } else {
                        std::cout << "[自瞄错误] 系统鼠标移动失败！" << std::endl;
                    }
                } else {
                    std::cout << "[平滑自瞄] 移动距离太小，跳过微调" << std::endl;
                }
            } else {
                std::cout << "[目标选择] 自瞄圆环内无有效目标" << std::endl;
            }
        }

        // 5. Render with detections (use original captured texture for display)
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        if (!renderer.RenderDetections(capture.GetCapturedTexture().Get(), detections.data(), detections.size(), renderedTexture)) {
            continue;
        }

        // 6. Display the rendered texture
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + inferenceTime + displayTime;

        // Show performance info every 30 frames
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            std::cout << "[" << frameCount << "] "
                      << "Capture:" << std::fixed << std::setprecision(2) << captureTime << "ms "
                      << "Preprocess:" << preprocessTime << "ms "
                      << "Inference:" << inferenceTime << "ms "
                      << "Render:" << displayTime << "ms "
                      << "Total:" << totalTime << "ms | "
                      << "FPS: " << std::setprecision(1) << fps << " | "
                      << "Detections: " << detections.size() << std::endl;
        }

        // ESC key check
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    // Simple aiming system doesn't need explicit stopping
    std::cout << "Simple aiming system completed" << std::endl;

    std::cout << "实时推理测试完成！" << std::endl;
    return true;
}

int main() {
    std::cout << "=== AimBot 实时推理应用程序 ===" << std::endl;
    std::cout << "本程序捕获屏幕中心320x320区域" << std::endl;
    std::cout << "并运行AI推理实时检测头部和身体目标。" << std::endl;
    std::cout << std::endl;

    std::cout << "选择测试模式:" << std::endl;
    std::cout << "1. 简单屏幕截图 (无AI推理)" << std::endl;
    std::cout << "2. 实时AI推理与检测显示" << std::endl;
    std::cout << "请输入选择 (1-2): ";

    int choice;
    std::cin >> choice;
    std::cin.ignore(); // Clear input buffer

    bool success = false;

    switch (choice) {
        case 1:
            success = RunSimpleScreenCapture();
            break;
        case 2:
            success = RunRealtimeInference();
            break;
        default:
            std::cout << "无效选择。运行简单屏幕截图..." << std::endl;
            success = RunSimpleScreenCapture();
            break;
    }

    std::cout << std::endl;
    if (success) {
        std::cout << "程序运行完成！" << std::endl;
    } else {
        std::cout << "程序运行失败！" << std::endl;
    }

    std::cout << "按回车键退出..." << std::endl;
    std::cin.get();

    return success ? 0 : 1;
}
