#include <iostream>
#include <iomanip>
#include <windows.h>
#include <d3d11.h>
#include <wrl/client.h>
#include <cmath>
#include <cfloat>

// Core project headers
#include "screen_capture.h"
#include "gpu_preprocessor.h"
#include "gpu_renderer.h"
#include "inference_engine.h"
// #include "kmbox_controller.h"  // 暂时禁用
// #include "simple_aiming.h"  // 暂时禁用

using Microsoft::WRL::ComPtr;

// Simple screen capture test - capture, preprocess and display
bool RunSimpleScreenCapture() {
    std::cout << "=== Simple Screen Capture Test ===" << std::endl;
    std::cout << "Testing: Screen Capture -> GPU Preprocessing -> Display" << std::endl;
    std::cout << "Press ESC to exit" << std::endl;

    // Initialize screen capture
    std::cout << "Initializing screen capture..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "Failed to initialize screen capture" << std::endl;
        return false;
    }
    std::cout << "Screen capture initialized successfully" << std::endl;

    // Initialize GPU preprocessor with center 320x320 ROI
    std::cout << "Initializing GPU preprocessor..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;   // Screen resolution
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);  // Center 320x320 region
    params.normalizationScale = 1.0f;  // No scaling, keep original colors
    params.meanR = 0.0f;  // No mean subtraction
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;   // No standard deviation scaling
    params.stdG = 1.0f;
    params.stdB = 1.0f;
    
    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "Failed to initialize GPU preprocessor" << std::endl;
        return false;
    }
    
    // Print ROI info
    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU preprocessor initialized successfully" << std::endl;
    std::cout << "ROI: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // Initialize GPU renderer for format conversion
    std::cout << "Initializing GPU renderer..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = false;
    renderConfig.showBoundingBoxes = false;
    
    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize GPU renderer" << std::endl;
        return false;
    }
    std::cout << "GPU renderer initialized successfully" << std::endl;

    // Initialize GPU display window
    std::cout << "Initializing GPU display window..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize display window" << std::endl;
        return false;
    }
    std::cout << "Display window initialized successfully" << std::endl;

    std::cout << "\nStarting simple screen capture..." << std::endl;
    std::cout << "A window will show the center 320x320 region of your screen" << std::endl;
    std::cout << "Format: [Frame] Capture:Xms Preprocess:Xms Render:Xms Total:Xms | FPS: X" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    while (!displayWindow.ShouldClose()) {
        // Process window messages
        displayWindow.ProcessMessages();

        frameCount++;

        // High precision timing
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. Capture screen
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. Preprocess (extract center 320x320 region and convert BGRA to RGB)
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. Convert format and render (R32G32B32A32_FLOAT -> R8G8B8A8_UNORM)
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        // No detections for simple display, just format conversion
        if (!renderer.RenderDetections(preprocessedTexture.Get(), nullptr, 0, renderedTexture)) {
            continue;
        }
        
        // 4. Display the rendered texture
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + displayTime;

        // Show performance info every 30 frames
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);
            
            std::cout << "[" << frameCount << "] "
                      << "Capture:" << std::fixed << std::setprecision(2) << captureTime << "ms "
                      << "Preprocess:" << preprocessTime << "ms "
                      << "Render:" << displayTime << "ms "
                      << "Total:" << totalTime << "ms | "
                      << "FPS: " << std::setprecision(1) << fps << std::endl;
        }

        // ESC key check
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    std::cout << "Simple screen capture test completed!" << std::endl;
    return true;
}

// Real-time inference with display
bool RunRealtimeInference() {
    std::cout << "=== Real-time Inference Test ===" << std::endl;
    std::cout << "Testing: Screen Capture -> GPU Preprocessing -> AI Inference -> Display" << std::endl;
    std::cout << "Press ESC to exit" << std::endl;

    // Initialize screen capture
    std::cout << "Initializing screen capture..." << std::endl;
    ScreenCapture capture;
    if (!capture.Initialize()) {
        std::cout << "Failed to initialize screen capture" << std::endl;
        return false;
    }
    std::cout << "Screen capture initialized successfully" << std::endl;

    // Initialize GPU preprocessor with center 320x320 ROI
    std::cout << "Initializing GPU preprocessor..." << std::endl;
    GPUPreprocessor preprocessor;
    PreprocessParams params;
    params.screenWidth = 1920;   // Screen resolution
    params.screenHeight = 1080;
    params.roiConfig = ROIConfig::CenterFixed(320, 320);  // Center 320x320 region
    params.normalizationScale = 1.0f;  // No scaling for now
    params.meanR = 0.0f;  // No mean subtraction
    params.meanG = 0.0f;
    params.meanB = 0.0f;
    params.stdR = 1.0f;   // No standard deviation scaling
    params.stdG = 1.0f;
    params.stdB = 1.0f;

    if (!preprocessor.Initialize(capture.GetDevice(), params)) {
        std::cout << "Failed to initialize GPU preprocessor" << std::endl;
        return false;
    }

    // Print ROI info
    int roiX, roiY, roiWidth, roiHeight;
    preprocessor.GetCurrentROI(roiX, roiY, roiWidth, roiHeight);
    std::cout << "GPU preprocessor initialized successfully" << std::endl;
    std::cout << "ROI: (" << roiX << ", " << roiY << ") " << roiWidth << "x" << roiHeight << std::endl;

    // Initialize inference engine
    std::cout << "Initializing inference engine..." << std::endl;
    InferenceEngine inferenceEngine;
    InferenceConfig inferenceConfig;
    inferenceConfig.modelPath = "models/PUBGV8_320.onnx";
    inferenceConfig.confidenceThreshold = 0.5f;
    inferenceConfig.useGPU = true;

    if (!inferenceEngine.Initialize(capture.GetDevice().Get(), inferenceConfig)) {
        std::cout << "Failed to initialize inference engine" << std::endl;
        return false;
    }
    std::cout << "Inference engine initialized successfully" << std::endl;

    // Initialize GPU renderer for format conversion and detection display
    std::cout << "Initializing GPU renderer..." << std::endl;
    GPURenderer renderer;
    GPURenderConfig renderConfig;
    renderConfig.renderWidth = 320;
    renderConfig.renderHeight = 320;
    renderConfig.modelWidth = 320;
    renderConfig.modelHeight = 320;
    renderConfig.showCrosshair = true;   // Show crosshair
    renderConfig.showBoundingBoxes = true;  // Show detection boxes

    if (!renderer.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize GPU renderer" << std::endl;
        return false;
    }
    std::cout << "GPU renderer initialized successfully" << std::endl;

    // TODO: Initialize KMBox hardware mouse controller (temporarily disabled)
    std::cout << "KMBox hardware mouse controller temporarily disabled" << std::endl;
    bool kmboxConnected = false;  // 暂时设为false

    // Initialize GPU display window
    std::cout << "Initializing GPU display window..." << std::endl;
    GPUDisplayWindow displayWindow;
    if (!displayWindow.Initialize(capture.GetDevice().Get(), renderConfig)) {
        std::cout << "Failed to initialize display window" << std::endl;
        return false;
    }
    std::cout << "Display window initialized successfully" << std::endl;

    std::cout << "\nStarting real-time inference..." << std::endl;
    std::cout << "A window will show the center 320x320 region with AI detections" << std::endl;
    std::cout << "Window Controls:" << std::endl;
    std::cout << "  - F1: Toggle always on top (currently ON)" << std::endl;
    std::cout << "  - ESC: Exit program" << std::endl;
    std::cout << "Aiming Controls:" << std::endl;
    std::cout << "  - Right Mouse Button: Activate smooth aiming" << std::endl;
    std::cout << "Hardware Mouse:" << std::endl;
    std::cout << "  - KMBox Status: " << (kmboxConnected ? "CONNECTED" : "DISCONNECTED") << std::endl;
    std::cout << "  - Mouse Control: " << (kmboxConnected ? "HARDWARE (KMBox)" : "SYSTEM (Fallback)") << std::endl;
    std::cout << "Format: [Frame] Capture:Xms Preprocess:Xms Inference:Xms Render:Xms Total:Xms | FPS: X | Detections: X" << std::endl;

    int frameCount = 0;
    DWORD startTime = GetTickCount();

    while (!displayWindow.ShouldClose()) {
        // Process window messages
        displayWindow.ProcessMessages();

        frameCount++;

        // High precision timing
        LARGE_INTEGER freq, start, end;
        QueryPerformanceFrequency(&freq);

        // 1. Capture screen
        QueryPerformanceCounter(&start);
        if (!capture.CaptureFrame()) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double captureTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 2. Preprocess (extract center 320x320 region and convert BGRA to RGB)
        QueryPerformanceCounter(&start);
        ComPtr<ID3D11Texture2D> preprocessedTexture;
        if (!preprocessor.PreprocessTexture(capture.GetCapturedTexture(), preprocessedTexture)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double preprocessTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 3. Run AI inference
        QueryPerformanceCounter(&start);
        std::vector<Detection> detections;
        if (!inferenceEngine.RunInference(preprocessedTexture.Get(), detections)) {
            continue;
        }
        QueryPerformanceCounter(&end);
        double inferenceTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        // 4. TODO: Process detections with simple aiming system (temporarily disabled)
        // simpleAiming.ProcessDetections(detections);

        // Step 2: Target selection algorithm with KMBox hardware control
        bool aimKeyPressed = (GetAsyncKeyState(0x02) & 0x8000) != 0;

        // TODO: KMBox按键监控 (暂时禁用)
        // if (kmboxConnected && kmboxController.IsEnabled()) {
        //     aimKeyPressed = aimKeyPressed || kmboxController.IsRightButtonPressed();
        // }

        if (aimKeyPressed && !detections.empty()) {
            std::cout << "[Aiming Test] Found " << detections.size() << " targets, right mouse button pressed" << std::endl;

            // Target selection parameters
            const float SCREEN_CENTER_X = 160.0f;  // Center of 320x320 region
            const float SCREEN_CENTER_Y = 160.0f;
            const float AIM_CIRCLE_RADIUS = 180.0f;
            const int PRIMARY_CLASS = 0;  // Head priority
            const int SECONDARY_CLASS = 1; // Body secondary

            // Find targets within aim circle
            Detection* bestTarget = nullptr;
            float minDistance = FLT_MAX;

            // First pass: look for primary targets (heads)
            for (auto& det : detections) {
                float centerX = det.x + det.width / 2.0f;
                float centerY = det.y + det.height / 2.0f;

                // Calculate distance from crosshair
                float dx = centerX - SCREEN_CENTER_X;
                float dy = centerY - SCREEN_CENTER_Y;
                float distance = sqrt(dx * dx + dy * dy);

                // Check if target is within aim circle and is primary class
                if (distance <= AIM_CIRCLE_RADIUS && det.classId == PRIMARY_CLASS && distance < minDistance) {
                    bestTarget = &det;
                    minDistance = distance;
                }
            }

            // Second pass: if no primary target found, look for secondary targets
            if (!bestTarget) {
                minDistance = FLT_MAX;
                for (auto& det : detections) {
                    float centerX = det.x + det.width / 2.0f;
                    float centerY = det.y + det.height / 2.0f;

                    float dx = centerX - SCREEN_CENTER_X;
                    float dy = centerY - SCREEN_CENTER_Y;
                    float distance = sqrt(dx * dx + dy * dy);

                    if (distance <= AIM_CIRCLE_RADIUS && det.classId == SECONDARY_CLASS && distance < minDistance) {
                        bestTarget = &det;
                        minDistance = distance;
                    }
                }
            }

            // If we found a target, move mouse to it
            if (bestTarget) {
                float targetX = bestTarget->x + bestTarget->width / 2.0f;
                float targetY = bestTarget->y + bestTarget->height / 2.0f;

                // Adjust aim point based on target class
                if (bestTarget->classId == 0) {
                    // Head: aim slightly higher
                    targetY = bestTarget->y + bestTarget->height * 0.13f;
                } else {
                    // Body: aim at center
                    targetY = bestTarget->y + bestTarget->height * 0.5f;
                }

                // Step 3: Smooth movement algorithm
                float deltaX = targetX - SCREEN_CENTER_X;
                float deltaY = targetY - SCREEN_CENTER_Y;

                // Smooth movement parameters
                const float SMOOTH_FACTOR = 0.3f;  // How much of the distance to move (0.1 = very smooth, 1.0 = instant)
                const float MIN_MOVE_THRESHOLD = 1.0f;  // Minimum movement to avoid micro-movements

                // Apply smoothing
                float smoothMoveX = deltaX * SMOOTH_FACTOR;
                float smoothMoveY = deltaY * SMOOTH_FACTOR;

                // Only move if the movement is significant enough
                if (abs(smoothMoveX) > MIN_MOVE_THRESHOLD || abs(smoothMoveY) > MIN_MOVE_THRESHOLD) {
                    int moveX = static_cast<int>(round(smoothMoveX));
                    int moveY = static_cast<int>(round(smoothMoveY));

                    bool moveSuccess = false;
                    std::string moveMethod = "SYSTEM";

                    // TODO: 优先使用KMBox硬件鼠标控制 (暂时禁用)
                    // if (kmboxConnected && kmboxController.IsEnabled()) {
                    //     if (kmboxController.MoveMouseAuto(moveX, moveY)) {
                    //         moveSuccess = true;
                    //         moveMethod = "KMBOX";
                    //     } else {
                    //         std::cout << "[KMBox] Hardware move failed, falling back to system mouse" << std::endl;
                    //     }
                    // }

                    // 使用系统鼠标移动
                    POINT currentPos;
                    if (GetCursorPos(&currentPos)) {
                        if (SetCursorPos(currentPos.x + moveX, currentPos.y + moveY)) {
                            moveSuccess = true;
                            moveMethod = "SYSTEM";
                        }
                    }

                    if (moveSuccess) {
                        std::cout << "[" << moveMethod << " Aiming] Moved to " << (bestTarget->classId == 0 ? "HEAD" : "BODY")
                                  << " target, smooth moved by (" << moveX << ", " << moveY << ")" << std::endl;
                    } else {
                        std::cout << "[Aiming ERROR] System mouse movement failed!" << std::endl;
                    }
                } else {
                    std::cout << "[Smooth Aiming] Movement too small, skipping micro-adjustment" << std::endl;
                }
            } else {
                std::cout << "[Target Selection] No valid targets within aim circle" << std::endl;
            }
        }

        // 5. Render with detections (use original captured texture for display)
        QueryPerformanceCounter(&start);
        ID3D11Texture2D* renderedTexture = nullptr;
        if (!renderer.RenderDetections(capture.GetCapturedTexture().Get(), detections.data(), detections.size(), renderedTexture)) {
            continue;
        }

        // 6. Display the rendered texture
        displayWindow.Present(renderedTexture);
        QueryPerformanceCounter(&end);
        double displayTime = (double)(end.QuadPart - start.QuadPart) * 1000.0 / freq.QuadPart;

        double totalTime = captureTime + preprocessTime + inferenceTime + displayTime;

        // Show performance info every 30 frames
        if (frameCount % 30 == 0) {
            DWORD currentTime = GetTickCount();
            double fps = frameCount * 1000.0 / (currentTime - startTime);

            std::cout << "[" << frameCount << "] "
                      << "Capture:" << std::fixed << std::setprecision(2) << captureTime << "ms "
                      << "Preprocess:" << preprocessTime << "ms "
                      << "Inference:" << inferenceTime << "ms "
                      << "Render:" << displayTime << "ms "
                      << "Total:" << totalTime << "ms | "
                      << "FPS: " << std::setprecision(1) << fps << " | "
                      << "Detections: " << detections.size() << std::endl;
        }

        // ESC key check
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            break;
        }
    }

    // Simple aiming system doesn't need explicit stopping
    std::cout << "Simple aiming system completed" << std::endl;

    std::cout << "Real-time inference test completed!" << std::endl;
    return true;
}

int main() {
    std::cout << "=== AimBot Real-time Inference Application ===" << std::endl;
    std::cout << "This application captures the center 320x320 region of your screen" << std::endl;
    std::cout << "and runs AI inference to detect heads and bodies in real-time." << std::endl;
    std::cout << std::endl;

    std::cout << "Choose test mode:" << std::endl;
    std::cout << "1. Simple screen capture (no AI inference)" << std::endl;
    std::cout << "2. Real-time AI inference with detection display" << std::endl;
    std::cout << "Enter choice (1-2): ";

    int choice;
    std::cin >> choice;
    std::cin.ignore(); // Clear input buffer

    bool success = false;

    switch (choice) {
        case 1:
            success = RunSimpleScreenCapture();
            break;
        case 2:
            success = RunRealtimeInference();
            break;
        default:
            std::cout << "Invalid choice. Running simple screen capture..." << std::endl;
            success = RunSimpleScreenCapture();
            break;
    }

    std::cout << std::endl;
    if (success) {
        std::cout << "Application completed successfully!" << std::endl;
    } else {
        std::cout << "Application failed!" << std::endl;
    }

    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();

    return success ? 0 : 1;
}
