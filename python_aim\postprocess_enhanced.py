# -*- coding: utf-8 -*-
# 文件名: postprocess_enhanced.py
# 功能: 增强版推理结果后处理模块，能自动适配不同YOLO模型格式

# --- 导入库 ---
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any, Optional, Union
import os

# 导入我们的模型探测器
try:
    from yolo_model_prober import YOLOModelProber
    PROBER_AVAILABLE = True
except ImportError:
    PROBER_AVAILABLE = False
    print("警告: 未找到YOLOModelProber模块，将使用通用处理逻辑。")

class EnhancedDetectionPostProcessor:
    """
    增强版目标检测结果后处理类，能够自动适配不同YOLO模型格式
    """
    def __init__(self, 
                 confidence_threshold: float = 0.5, 
                 nms_threshold: float = 0.1, 
                 class_names: List[str] = None,
                 model_path: Optional[str] = None,
                 force_model_type: Optional[str] = None,
                 num_classes: int = 0):
        """
        初始化后处理器
        
        Args:
            confidence_threshold: 置信度阈值，低于此值的检测将被过滤
            nms_threshold: 非极大抑制重叠阈值
            class_names: 类别名称列表
            model_path: YOLO模型文件路径，用于自动探测模型格式
            force_model_type: 强制指定模型类型 ('yolov5' 或 'yolov8')
            num_classes: 类别数量，如果为0则自动检测
        """
        self.confidence_threshold = confidence_threshold
        self.nms_threshold = nms_threshold
        
        # 默认类别名称，后续可能会根据模型探测结果调整
        self.class_names = class_names if class_names is not None else ["头部", "身体"]
        self.format_printed = False
        
        # 模型格式设置
        self.model_settings = {
            "needs_transpose": None,  # 是否需要转置
            "box_format": "xywh",     # 边界框格式
            "has_obj_confidence": True, # 是否有obj置信度
            "class_offset": 5,        # 类别分数起始位置
            "model_type": "unknown",  # 模型类型
        }
        
        # 如果强制指定了模型类型，则使用指定的设置
        if force_model_type:
            self._apply_forced_model_type(force_model_type, num_classes)
        # 如果提供了模型路径，尝试探测模型
        elif model_path and PROBER_AVAILABLE:
            self._probe_model(model_path)
        else:
            print("未提供模型路径或模型探测器不可用，将使用默认设置。")

    def _apply_forced_model_type(self, model_type: str, num_classes: int) -> None:
        """
        应用用户强制指定的模型类型设置
        
        Args:
            model_type: 指定的模型类型 ('yolov5' 或 'yolov8')
            num_classes: 类别数量
        """
        # 检查是否已经设置过同样的模型类型，避免重复输出
        if hasattr(self, '_model_type_set') and self._model_type_set == model_type.lower():
            return
            
        # 标记已经设置过此模型类型
        self._model_type_set = model_type.lower()
        
        print(f"用户指定模型类型: {model_type.upper()}")
        
        if model_type.lower() == "yolov5":
            self.model_settings.update({
                "needs_transpose": None,  # 设为None允许自动判断
                "has_obj_confidence": True,
                "class_offset": 5,
                "model_type": "yolov5"
            })
        elif model_type.lower() == "yolov8":
            self.model_settings.update({
                "needs_transpose": None,  # 设为None允许自动判断
                "has_obj_confidence": False,
                "class_offset": 4,
                "model_type": "yolov8"
            })
        else:
            print(f"警告: 未知模型类型 {model_type}，将使用默认设置。")
            return
            
        # 处理类别数量
        if num_classes > 0 and len(self.class_names) != num_classes:
            print(f"调整类别数量: {len(self.class_names)} -> {num_classes}")
            
            # 生成通用类别名称，确保数量与指定的类别数量匹配
            if len(self.class_names) < num_classes:
                # 生成新的通用类别名称
                for i in range(len(self.class_names), num_classes):
                    self.class_names.append(f"类别{i}")
            else:
                # 如果提供的类别名称过多，截断到匹配指定类别数
                self.class_names = self.class_names[:num_classes]
                
        # 打印设置信息
        print("\n=== 模型设置 (用户指定) ===")
        print(f"模型类型: {self.model_settings['model_type']}")
        print(f"需要转置: {self.model_settings['needs_transpose']}")
        print(f"有对象置信度: {self.model_settings['has_obj_confidence']}")
        print(f"类别起始位置: {self.model_settings['class_offset']}")
        print(f"检测框格式: {self.model_settings['box_format']}")
        print(f"类别数量: {len(self.class_names)}")
        print(f"类别名称: {self.class_names}")
        print("==========================\n")

    def _probe_model(self, model_path: str) -> None:
        """
        探测模型格式并更新设置
        
        Args:
            model_path: 模型文件路径
        """
        try:
            # 确保文件存在
            if not os.path.exists(model_path):
                print(f"错误: 模型文件不存在 {model_path}")
                return
                
            print(f"正在探测模型: {model_path}")
            prober = YOLOModelProber(verbose=False)
            prober.probe(model_path)
            
            # 获取推荐设置
            settings = prober.get_recommended_processor_settings()
            model_info = prober.get_model_info()
            
            # 更新设置
            self.model_settings.update(settings)
            self.model_settings["model_type"] = model_info["model_type"]
            # 设置needs_transpose为None，让处理函数根据实际输入自动判断
            self.model_settings["needs_transpose"] = None
            
            # 自动适配类别数量
            num_classes = model_info["format_info"]["num_classes"]
            if len(self.class_names) != num_classes:
                print(f"警告: 类别数量不匹配 - 提供了{len(self.class_names)}个类别名称，但模型有{num_classes}个类别")
                print("正在自动生成通用类别名称...")
                
                # 生成通用类别名称，确保数量与模型匹配
                # 保留原有的类别名称，不足的部分自动生成
                if len(self.class_names) < num_classes:
                    # 生成新的通用类别名称
                    for i in range(len(self.class_names), num_classes):
                        self.class_names.append(f"类别{i}")
                else:
                    # 如果提供的类别名称过多，截断到匹配模型类别数
                    self.class_names = self.class_names[:num_classes]
                
                print(f"自动生成的类别名称: {self.class_names}")
            
            # 打印设置信息
            print("\n=== 模型格式自动探测结果 ===")
            print(f"模型类型: {self.model_settings['model_type']}")
            print(f"需要转置: {self.model_settings['needs_transpose']}")
            print(f"有对象置信度: {self.model_settings['has_obj_confidence']}")
            print(f"类别起始位置: {self.model_settings['class_offset']}")
            print(f"检测框格式: {self.model_settings['box_format']}")
            print(f"类别数量: {num_classes}")
            print(f"类别名称: {self.class_names}")
            print("============================\n")
            
        except Exception as e:
            print(f"模型探测失败: {str(e)}")
            print("将使用默认设置继续。")
    
    def process(self, output: any, original_img: np.ndarray, input_shape: Tuple[int, int],
                force_model_type: Optional[str] = None, num_classes: int = 0,
                class_names: List[str] = None) -> Tuple[np.ndarray, List[Dict]]:
        """
        统一处理所有YOLO格式的模型输出。
        
        Args:
            output: 模型的原始输出
            original_img: 原始输入图像
            input_shape: 模型的输入尺寸
            force_model_type: 强制指定模型类型（覆盖初始化时的设置）
            num_classes: 强制指定类别数量（覆盖初始化时的设置）
            class_names: 强制指定类别名称（覆盖初始化时的设置）
            
        Returns:
            (处理后的图像, 检测结果列表)
        """
        # 如果需要临时覆盖模型类型设置
        temp_settings_applied = False
        original_settings = None
        original_class_names = None
        
        # 只有当设置发生实际变化时才应用临时设置
        needs_model_type_update = (force_model_type and 
                                  (not hasattr(self, '_model_type_set') or 
                                   self._model_type_set != force_model_type.lower()))
        
        needs_class_names_update = (class_names and class_names != self.class_names)
        
        if needs_model_type_update or needs_class_names_update:
            # 保存原始设置
            temp_settings_applied = True
            original_settings = self.model_settings.copy()
            original_class_names = self.class_names.copy()
            
            # 应用临时设置
            if needs_model_type_update:
                self._apply_forced_model_type(force_model_type, num_classes)
            
            # 应用类别名称
            if needs_class_names_update:
                self.class_names = class_names
                
        # 确保类别名称列表不为空，如果为空则创建默认类别名称
        if not self.class_names:
            print("警告: 类别名称列表为空，创建默认类别名称")
            self.class_names = ["类别0", "类别1"]
        
        try:
            # --- 1. 数据清洗和塑形 ---
            try:
                # a. 解包：应对嵌套元组输出
                tensor = output
                while isinstance(tensor, tuple):
                    tensor = tensor[0]
                
                # b. 判断是否需要转置
                needs_transpose = self.model_settings["needs_transpose"]
                
                # 如果模型设置尚未确定，则进行自动判断
                if needs_transpose is None:
                    # 检测框数量必然远大于特征维度
                    # 如果第二维小于第三维，则格式为 [1, 特征维度, 检测框数量]，需要转置
                    # 如果第二维大于第三维，则格式为 [1, 检测框数量, 特征维度]，不需要转置
                    if len(tensor.shape) == 3:
                        # 比较第二维和第三维的大小
                        if tensor.shape[1] < tensor.shape[2]:
                            # 例如 [1, 7, 6300] 格式，需要转置
                            needs_transpose = True
                            if not self.format_printed:
                                print(f"自动检测到转置格式 {tensor.shape}, 第二维({tensor.shape[1]})小于第三维({tensor.shape[2]})，推断为 [batch, 特征维度, 检测框数量] 格式，正在转置...")
                        else:
                            # 例如 [1, 6300, 7] 格式，不需要转置
                            needs_transpose = False
                            if not self.format_printed:
                                print(f"检测到标准格式 {tensor.shape}, 第二维({tensor.shape[1]})大于第三维({tensor.shape[2]})，推断为 [batch, 检测框数量, 特征维度] 格式")
                    else:
                        needs_transpose = False
                
                # 执行转置
                if needs_transpose and len(tensor.shape) == 3:
                    if not self.format_printed:
                        print(f"转置前形状: {tensor.shape}")
                    tensor = tensor.transpose(0, 2, 1)
                    if not self.format_printed:
                        print(f"转置后形状: {tensor.shape}")
                
                # c. 展平：将 (batch, boxes, dims) 统一为 (N, dims) 格式
                predictions = np.squeeze(tensor, axis=0)
                
                if not self.format_printed:
                    print(f"处理的张量形状: {predictions.shape}")
                    self.format_printed = True

            except Exception as e:
                if not self.format_printed:
                    print(f"数据清洗阶段出错: {e}, 输入类型: {type(output)}")
                    self.format_printed = True
                return original_img, []

            # --- 2. 解析预测结果 ---
            # 检查数据维度是否有效
            if predictions.shape[1] < 4:  # 至少需要xywh四个坐标
                return original_img, []

            # --- 确定数据格式 ---
            has_obj_confidence = self.model_settings["has_obj_confidence"]
            class_offset = self.model_settings["class_offset"]
            
            # 提取边界框坐标
            boxes_xywh = predictions[:, :4]
            
            # 处理置信度和类别分数
            if has_obj_confidence:
                # YOLOv5格式: [x,y,w,h,obj,class1,class2,...]
                obj_scores = predictions[:, 4]
                class_scores = predictions[:, class_offset:]
                # 最终置信度是obj_score和最大class_score的乘积
                class_scores_max = np.max(class_scores, axis=1)
                scores = obj_scores * class_scores_max
                class_indices = np.argmax(class_scores, axis=1)
            else:
                # YOLOv8格式: [x,y,w,h,class1,class2,...]
                class_scores = predictions[:, 4:]
                # 最终置信度就是最大的class_score
                scores = np.max(class_scores, axis=1)
                class_indices = np.argmax(class_scores, axis=1)

            # 根据置信度阈值创建掩码，进行过滤
            mask = scores > self.confidence_threshold
            if not np.any(mask):
                return original_img, []

            # 应用掩码，得到最终用于后续处理的数据
            filtered_boxes = boxes_xywh[mask]
            filtered_scores = scores[mask]
            class_indices = class_indices[mask]

            # --- 3. 坐标转换 ---
            img_h, img_w = original_img.shape[:2]
            
            # 检查坐标是否为归一化 (0-1之间)
            if np.max(filtered_boxes) <= 1.0:
                # (cx, cy, w, h) -> (x1, y1, x2, y2)
                cx, cy, w, h = filtered_boxes.T
                x1 = (cx - w / 2) * img_w
                y1 = (cy - h / 2) * img_h
                x2 = (cx + w / 2) * img_w
                y2 = (cy + h / 2) * img_h
                boxes_xyxy = np.stack((x1, y1, x2, y2), axis=1)
            else:
                # 如果是像素坐标 (cx, cy, w, h) -> (x1, y1, x2, y2)
                cx, cy, w, h = filtered_boxes.T
                x1 = cx - w / 2
                y1 = cy - h / 2
                x2 = cx + w / 2
                y2 = cy + h / 2
                boxes_xyxy = np.stack((x1, y1, x2, y2), axis=1)

            # --- 4. 按类别进行非极大值抑制 (Per-Class NMS) ---
            detections = []
            # 获取所有不重复的类别ID
            unique_class_ids = np.unique(class_indices)

            # 遍历每个类别ID
            for class_id in unique_class_ids:
                # a. 筛选出当前类别的所有检测结果
                class_mask = (class_indices == class_id)
                class_boxes = boxes_xyxy[class_mask]
                class_scores = filtered_scores[class_mask]

                # 如果这个类别没有检测到任何框，就跳到下一个类别
                if len(class_boxes) == 0:
                    continue

                # b. 对当前类别的检测结果进行NMS
                indices = cv2.dnn.NMSBoxes(class_boxes.tolist(), class_scores.tolist(), 
                                          self.confidence_threshold, self.nms_threshold)

                # 如果NMS之后有保留下来的框
                if len(indices) > 0:
                    # c. 从NMS结果中提取最终的检测信息
                    final_boxes_for_class = class_boxes[indices.flatten()]
                    final_scores_for_class = class_scores[indices.flatten()]

                    # 遍历这个类别中最终保留的每一个框
                    for i in range(len(final_boxes_for_class)):
                        x1, y1, x2, y2 = final_boxes_for_class[i]
                        score = final_scores_for_class[i]
                        
                        # 将坐标裁剪到图像边界内
                        x = max(0, int(x1))
                        y = max(0, int(y1))
                        w = max(0, int(x2 - x1))
                        h = max(0, int(y2 - y1))
                        
                        # 确保类别ID不会越界
                        if len(self.class_names) == 0:
                            # 如果没有类别名称，则创建默认值
                            self.class_names = [f"类别{i}" for i in range(int(max(unique_class_ids)) + 1)]
                            print(f"警告: 自动生成类别名称: {self.class_names}")
                        
                        # 如果类别ID超出范围，则动态扩展类别名称列表
                        if int(class_id) >= len(self.class_names):
                            original_len = len(self.class_names)
                            # 添加足够的新类别名称
                            for i in range(original_len, int(class_id) + 1):
                                self.class_names.append(f"类别{i}")
                            print(f"警告: 类别ID {int(class_id)} 超出范围，已扩展类别名称至 {len(self.class_names)} 个")
                            
                        safe_class_id = int(class_id)
                        
                        # 将整理好的信息添加到结果列表
                        detections.append({
                            "class_id": safe_class_id,
                            "class_name": self.class_names[safe_class_id],
                            "confidence": float(score),
                            "box": [x, y, w, h]
                        })

            # --- 5. 绘制结果 ---
            result_img = original_img.copy()
            self.draw_detections(result_img, detections)
            
            # 返回处理结果
            return result_img, detections
            
        finally:
            # 如果应用了临时设置，恢复原始设置
            if temp_settings_applied:
                if original_settings:
                    self.model_settings = original_settings
                if original_class_names:
                    self.class_names = original_class_names

    def draw_detections(self, image: np.ndarray, detections: List[Dict]) -> None:
        """
        在图像上绘制检测结果
        
        Args:
            image: 要绘制的图像
            detections: 检测结果列表
        """
        colors = {0: (0, 0, 255), 1: (0, 255, 0)}  # BGR
        for det in detections:
            box = det["box"]
            x, y, w, h = box
            class_id = det["class_id"]
            label = f"{det['class_name']}: {det['confidence']:.2f}"
            color = colors.get(class_id, (255, 255, 0))
            
            cv2.rectangle(image, (x, y), (x + w, y + h), color, 2)
            cv2.putText(image, label, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

# 测试程序
if __name__ == "__main__":
    import sys
    
    # 如果命令行参数中提供了模型路径，使用它
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = "models/1.onnx"  # 默认测试模型
        
    # 初始化增强版后处理器，自动探测模型格式
    processor = EnhancedDetectionPostProcessor(
        confidence_threshold=0.5,
        nms_threshold=0.4,
        model_path=model_path
    )
    
    print("\n=== 测试后处理功能 ===")
    print("1. 加载测试图片...")
    test_image = cv2.imread("test.jpg")
    
    if test_image is None:
        print("错误：无法读取测试图片，请确保存在有效的测试图片！")
    else:
        print("2. 模拟模型输出...")
        # 创建一个假的模型输出进行测试
        fake_output = np.array([[
            [0.2, 0.3, 0.2, 0.3, 0.95, 0.9, 0.1],  # 类别0，高置信度
            [0.7, 0.6, 0.3, 0.4, 0.98, 0.1, 0.9],  # 类别1，高置信度
        ]])
        
        print("3. 执行后处理...")
        result_image, detections = processor.process(fake_output, test_image, (640, 640))
        
        print(f"\n检测到 {len(detections)} 个目标:")
        for i, det in enumerate(detections):
            print(f"  目标 #{i+1}: 类别='{det['class_name']}', "
                  f"置信度={det['confidence']:.2f}, 位置={det['box']}")
        
        if len(detections) > 0:
            print("\n4. 显示结果图像...")
            cv2.imshow("检测结果", result_image)
            print("请按任意键关闭窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        else:
            print("未检测到任何目标，无法显示结果。") 