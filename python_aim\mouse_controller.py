# -*- coding: utf-8 -*-
# 文件名: mouse_controller.py
# 功能: 封装Windows SendInput API，用于精确的鼠标控制

import win32api
import win32con
import ctypes
import time
import os

# 自动添加bin目录到DLL搜索路径
bin_path = os.path.join(os.path.dirname(__file__), 'bin')
if hasattr(os, 'add_dll_directory'):
    os.add_dll_directory(bin_path)

dll_path = os.path.join(bin_path, 'MouseControl.dll')
try:
    mouse_driver = ctypes.CDLL(dll_path)
    GHUB_AVAILABLE = True
except Exception as e:
    mouse_driver = None
    GHUB_AVAILABLE = False
    print(f"[GHUB] MouseControl.dll加载失败: {e}")

# --- CTypes 结构体定义 ---
# 这些结构体直接对应于Windows API中的定义，用于与SendInput函数交互。

# 定义一些在Windows API中常用到的数据类型
LONG = ctypes.c_long
DWORD = ctypes.c_ulong
ULONG_PTR = ctypes.POINTER(DWORD)

# MOUSEINPUT结构体：包含了模拟鼠标事件所需的信息
class MOUSEINPUT(ctypes.Structure):
    _fields_ = [("dx", LONG),             # x方向的绝对位置或相对移动量
                ("dy", LONG),             # y方向的绝对位置或相对移动量
                ("mouseData", DWORD),     # 如果是鼠标滚轮事件，这里是滚轮滚动的量
                ("dwFlags", DWORD),       # 一组标志位，定义了鼠标移动和点击的类型
                ("time", DWORD),          # 事件的时间戳，通常设为0让系统提供
                ("dwExtraInfo", ULONG_PTR)] # 与事件关联的额外信息，通常设为0

# INPUT结构体：这是一个通用的输入事件结构体，可以是鼠标、键盘或硬件事件
class INPUT(ctypes.Structure):
    class _INPUT_UNION(ctypes.Union):
        _fields_ = [("mi", MOUSEINPUT)] # 我们只关心鼠标输入(mi)
    
    _anonymous_ = ("_input",)
    _fields_ = [("type", DWORD),         # 输入事件的类型 (0 for mouse)
                ("_input", _INPUT_UNION)]

# --- SendInput API 的常量定义 ---
INPUT_MOUSE = 0                  # 输入事件类型为鼠标
MOUSEEVENTF_MOVE = 0x0001        # 标志位：表示这是一个移动事件

# --- 封装好的鼠标控制函数 ---

# 鼠标按键定义
AIM_KEY_PRIO_1 = 0x05  # 优先级1，默认为鼠标下侧键
AIM_KEY_PRIO_2 = 0x06  # 优先级2，默认为鼠标上侧键

# KMBoxNet状态全局变量
KMBOXNET_AVAILABLE = False  # 是否可用KMBoxNet模块
KMBOXNET_INITIALIZED = False  # 是否已初始化KMBoxNet

try:
    import kmNet
    KMBOXNET_AVAILABLE = True
except ImportError:
    KMBOXNET_AVAILABLE = False


def init_kmboxnet(ip, port, uuid):
    """初始化KMBoxNet"""
    global KMBOXNET_INITIALIZED
    
    if not KMBOXNET_AVAILABLE:
        return False
        
    try:
        kmNet.init(ip, port, uuid)
        kmNet.monitor(1)  # 启用按键监听
        KMBOXNET_INITIALIZED = True
        return True
    except Exception as e:
        print(f"KMBoxNet初始化失败: {e}")
        KMBOXNET_INITIALIZED = False
        return False

def kmboxnet_move(dx, dy):
    """使用KMBoxNet移动鼠标"""
    if not KMBOXNET_INITIALIZED:
        return False
        
    try:
        kmNet.move(int(dx), int(dy))
        return True
    except Exception as e:
        print(f"KMBoxNet移动鼠标失败: {e}")
        return False

def is_key_pressed_kmboxnet(key_code):
    """使用KMBoxNet检测按键状态"""
    if not KMBOXNET_INITIALIZED:
        return False
    
    try:
        if key_code == 0x01:  # 鼠标左键
            return kmNet.isdown_left()
        elif key_code == 0x02:  # 鼠标右键
            return kmNet.isdown_right()
        elif key_code == 0x05:  # 鼠标下侧键
            return kmNet.isdown_side1()
        elif key_code == 0x06:  # 鼠标上侧键
            return kmNet.isdown_side2()
        return False
    except Exception as e:
        print(f"KMBoxNet按键检测失败: {e}")
        return False

def get_aim_priority():
    """获取当前按下的自瞄按键优先级
    返回值:
        0 - 没有按下自瞄键
        1 - 按下了优先级1的自瞄键 (针对类别0)
        2 - 按下了优先级2的自瞄键 (针对类别1)
    """
    # 优先使用KMBoxNet检测按键
    if KMBOXNET_INITIALIZED:
        if is_key_pressed_kmboxnet(AIM_KEY_PRIO_1):
            return 1
        elif is_key_pressed_kmboxnet(AIM_KEY_PRIO_2):
            return 2
        else:
            return 0
    
    # 回退到系统API
    try:
        if win32api.GetAsyncKeyState(AIM_KEY_PRIO_1) & 0x8000:
            return 1
        elif win32api.GetAsyncKeyState(AIM_KEY_PRIO_2) & 0x8000:
            return 2
        else:
            return 0
    except:
        return 0

def move_mouse(dx, dy):
    """移动鼠标
    参数:
        dx, dy: 横向和纵向的移动距离 (像素)
    """
    # 优先使用KMBoxNet移动鼠标
    if KMBOXNET_INITIALIZED:
        return kmboxnet_move(dx, dy)
    
    # 回退到系统API
    try:
        x, y = win32api.GetCursorPos()
        win32api.SetCursorPos((int(x + dx), int(y + dy)))
        return True
    except Exception as e:
        print(f"系统移动鼠标失败: {e}")
        return False

def move_mouse_ghub(dx: int, dy: int) -> bool:
    """
    使用MouseControl.dll驱动级相对移动鼠标
    """
    if not GHUB_AVAILABLE:
        print("[GHUB] MouseControl.dll不可用，无法驱动鼠标移动")
        return False
    try:
        mouse_driver.move_R(int(dx), int(dy))
        return True
    except Exception as e:
        print(f"[GHUB] 鼠标移动失败: {e}")
        return False

def mouse_left_down_ghub() -> bool:
    """
    使用MouseControl.dll驱动级按下鼠标左键
    """
    if not GHUB_AVAILABLE:
        print("[GHUB] MouseControl.dll不可用，无法按下左键")
        return False
    try:
        mouse_driver.click_Left_down()
        return True
    except Exception as e:
        print(f"[GHUB] 鼠标左键按下失败: {e}")
        return False

def mouse_left_up_ghub() -> bool:
    """
    使用MouseControl.dll驱动级松开鼠标左键
    """
    if not GHUB_AVAILABLE:
        print("[GHUB] MouseControl.dll不可用，无法松开左键")
        return False
    try:
        mouse_driver.click_Left_up()
        return True
    except Exception as e:
        print(f"[GHUB] 鼠标左键松开失败: {e}")
        return False

def mouse_left_down_kmbox():
    try:
        import kmNet
        kmNet.left(1)
        return True
    except Exception as e:
        print(f"[KMBox] 鼠标左键按下失败: {e}")
        return False

def mouse_left_up_kmbox():
    try:
        import kmNet
        kmNet.left(0)
        return True
    except Exception as e:
        print(f"[KMBox] 鼠标左键松开失败: {e}")
        return False

def is_key_pressed(key_code: int) -> bool:
    """
    检查指定虚拟键码的按键是否被按下。
    支持系统API和KMBoxNet。
    """
    # 优先用KMBoxNet
    if 'kmboxnet_is_key_pressed' in globals():
        try:
            return kmboxnet_is_key_pressed(key_code)
        except Exception:
            pass
    # 默认用系统API
    return (ctypes.windll.user32.GetAsyncKeyState(key_code) & 0x8000) != 0

# --- 测试代码 ---
# 如果直接运行这个文件，会执行以下测试代码
if __name__ == '__main__':
    print("--- 鼠标移动模块测试 ---")
    print("将在3秒后开始测试，请将鼠标放在屏幕中央。")
    time.sleep(3)

    print("测试1: 向右下方移动 (100, 100)")
    move_mouse(100, 100)
    time.sleep(1)

    print("测试2: 向左上方移动 (-200, -200)")
    move_mouse(-200, -200)
    time.sleep(1)
    
    print("测试3: 画一个正方形 (边长200像素)")
    move_mouse(200, 0)
    time.sleep(0.5)
    move_mouse(0, 200)
    time.sleep(0.5)
    move_mouse(-200, 0)
    time.sleep(0.5)
    move_mouse(0, -200)
    time.sleep(1)

    print("\n--- 按键检测测试 ---")
    print("请在10秒内分别按住你的鼠标【上侧键】和【下侧键】...")
    end_time = time.time() + 10
    while time.time() < end_time:
        priority = get_aim_priority()
        if priority == 1:
            print("\r自瞄模式: [优先类别0] (上侧键按下)", end="")
        elif priority == 2:
            print("\r自瞄模式: [优先类别1] (下侧键按下)", end="")
        else:
            print("\r自瞄模式: [未激活]                ", end="")
        time.sleep(0.05)
    print("\n--- 测试结束 ---") 