import time
import numpy as np

class AdaptivePIDController:
    """
    带积分分离的自适应PID控制器，优化自瞄移动
    
    特点:
    1. 根据误差大小动态调整积分系数(Ki)
    2. 远距离快速接近目标，近距离精确吸附
    3. 减少过冲现象同时提高对移动目标的跟踪能力
    """
    def __init__(self, kp=0.6, ki=0.4, kd=0.0, 
                 base_ki=0.0, max_ki=0.4, 
                 inner_threshold=10.0, outer_threshold=50.0,
                 windup_guard=10.0):
        """
        初始化自适应PID控制器
        
        参数:
            kp (float): 比例增益，控制响应强度
            ki (float): 积分增益最大值，减少稳态误差
            kd (float): 微分增益，提供阻尼减少过冲
            base_ki (float): 基础积分增益(大误差时使用)
            max_ki (float): 最大积分增益(小误差时使用)
            inner_threshold (float): 内阈值，误差小于此值时使用最大积分增益
            outer_threshold (float): 外阈值，误差大于此值时禁用积分项
            windup_guard (float): 积分项限制，防止积分饱和
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.base_ki = base_ki
        self.max_ki = max_ki
        self.inner_threshold = inner_threshold
        self.outer_threshold = outer_threshold
        self.windup_guard = windup_guard
        
        # 初始化状态变量
        self.last_error = np.array([0.0, 0.0])  # X和Y方向的上一次误差
        self.integral = np.array([0.0, 0.0])    # 误差积分项
        self.last_time = time.perf_counter()    # 上一次更新时间
        
    def reset(self):
        """重置PID控制器状态"""
        self.last_error = np.array([0.0, 0.0])
        self.integral = np.array([0.0, 0.0])
        self.last_time = time.perf_counter()
    
    def _calculate_adaptive_ki(self, error_magnitude):
        """
        计算自适应积分系数
        
        参数:
            error_magnitude (float): 误差向量的模长
            
        返回:
            float: 当前误差下的积分系数
        """
        # 超出外阈值，禁用积分作用
        if error_magnitude > self.outer_threshold:
            return 0.0
        
        # 小于内阈值，使用最大积分系数
        elif error_magnitude < self.inner_threshold:
            return self.max_ki
        
        # 在两个阈值之间，线性插值
        else:
            # 计算比例因子 (0到1之间)
            factor = 1.0 - (error_magnitude - self.inner_threshold) / (self.outer_threshold - self.inner_threshold)
            # 线性插值得到积分系数
            return self.base_ki + (self.max_ki - self.base_ki) * factor
        
    def compute(self, error, current_time=None):
        """
        计算自适应PID控制输出
        
        参数:
            error (numpy.ndarray): [dx, dy] 当前误差向量
            current_time (float): 当前时间戳，如果为None则自动获取
            
        返回:
            numpy.ndarray: PID控制输出 [move_dx, move_dy]
        """
        if current_time is None:
            current_time = time.perf_counter()
            
        # 计算时间差
        dt = current_time - self.last_time
        if dt <= 0:
            dt = 0.001  # 防止除零错误
            
        # 将误差转换为numpy数组
        error = np.array(error, dtype=np.float64)
        
        # 计算误差向量的模长
        error_magnitude = np.linalg.norm(error)
        
        # 根据误差大小计算自适应积分系数
        effective_ki = self._calculate_adaptive_ki(error_magnitude)
        
        # 计算误差导数 (变化率)
        delta_error = (error - self.last_error) / dt
        
        # 更新积分项
        self.integral += error * dt
        
        # 积分限制，防止积分饱和
        for i in range(len(self.integral)):
            if self.integral[i] > self.windup_guard:
                self.integral[i] = self.windup_guard
            elif self.integral[i] < -self.windup_guard:
                self.integral[i] = -self.windup_guard
                
        # 计算PID输出 (使用自适应积分系数)
        output = self.kp * error + effective_ki * self.integral + self.kd * delta_error
        
        # 更新状态
        self.last_error = error.copy()
        self.last_time = current_time
        
        # 输出诊断信息
        # print(f"Error: {error_magnitude:.2f}, Ki: {effective_ki:.4f}, Output: {np.linalg.norm(output):.2f}")
        
        return output
    
    def set_gains(self, kp=None, ki=None, kd=None, base_ki=None, max_ki=None):
        """更新PID增益参数"""
        if kp is not None:
            self.kp = kp
        if ki is not None:
            self.ki = ki  # 保持兼容性
            self.max_ki = ki  # 同时更新max_ki
        if kd is not None:
            self.kd = kd
        if base_ki is not None:
            self.base_ki = base_ki
        if max_ki is not None:
            self.max_ki = max_ki
            
    def set_thresholds(self, inner_threshold=None, outer_threshold=None):
        """更新积分分离阈值"""
        if inner_threshold is not None:
            self.inner_threshold = inner_threshold
        if outer_threshold is not None:
            self.outer_threshold = outer_threshold
            
    def get_gains(self):
        """获取当前PID增益参数"""
        return {
            "kp": self.kp, 
            "ki": self.ki,  # 保持兼容性 
            "kd": self.kd,
            "base_ki": self.base_ki,
            "max_ki": self.max_ki,
            "inner_threshold": self.inner_threshold,
            "outer_threshold": self.outer_threshold
        }
        
    def get_current_ki(self, error):
        """获取当前误差下的积分系数"""
        error_magnitude = np.linalg.norm(error)
        return self._calculate_adaptive_ki(error_magnitude) 