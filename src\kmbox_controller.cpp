#include "kmbox_controller.h"
#include "kmbox/kmboxNet.h"
#include <iostream>

KMBoxController::KMBoxController(const KMBoxConfig& config) 
    : config_(config), initialized_(false), connected_(false) {
    std::cout << "[KMBox] Controller created" << std::endl;
}

KMBoxController::~KMBoxController() {
    Disconnect();
}

bool KMBoxController::Initialize() {
    if (initialized_) {
        return connected_;
    }

    std::cout << "[KMBox] Connecting..." << std::endl;
    
    try {
        int result = kmNet_init(
            const_cast<char*>(config_.ip.c_str()), 
            const_cast<char*>(config_.port.c_str()), 
            const_cast<char*>(config_.mac.c_str())
        );
        
        if (result == 0) {
            initialized_ = true;
            connected_ = true;
            std::cout << "[K<PERSON><PERSON>] Connected!" << std::endl;
            return true;
        } else {
            std::cout << "[K<PERSON>ox] Failed: " << result << std::endl;
            return false;
        }
    } catch (...) {
        std::cout << "[KMBox] Exception" << std::endl;
        return false;
    }
}

void KMBoxController::Disconnect() {
    if (connected_) {
        connected_ = false;
        initialized_ = false;
    }
}

void KMBoxController::UpdateConfig(const KMBoxConfig& config) {
    config_ = config;
}

bool KMBoxController::MoveMouse(int dx, int dy) {
    if (!config_.enabled || !connected_ || (dx == 0 && dy == 0)) {
        return false;
    }

    try {
        int result = kmNet_mouse_move(static_cast<short>(dx), static_cast<short>(dy));
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::MoveMouseSmooth(int dx, int dy, int time_ms) {
    if (!config_.enabled || !connected_ || (dx == 0 && dy == 0)) {
        return false;
    }

    try {
        int result = kmNet_mouse_move_auto(dx, dy, time_ms);
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::MoveMouseAuto(int dx, int dy) {
    return MoveMouseSmooth(dx, dy, config_.smooth_move_time_ms);
}

bool KMBoxController::LeftClick() {
    return false;
}

bool KMBoxController::LeftDown() {
    return false;
}

bool KMBoxController::LeftUp() {
    return false;
}

bool KMBoxController::RightClick() {
    return false;
}

bool KMBoxController::RightDown() {
    return false;
}

bool KMBoxController::RightUp() {
    return false;
}

bool KMBoxController::IsRightButtonPressed() {
    return false;
}

bool KMBoxController::IsLeftButtonPressed() {
    return false;
}

bool KMBoxController::TestConnection() {
    if (!connected_) return false;
    
    try {
        int result = kmNet_mouse_move(0, 0);
        return result == 0;
    } catch (...) {
        return false;
    }
}
