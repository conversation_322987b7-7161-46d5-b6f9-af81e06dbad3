#include "kmbox_controller.h"
#include "kmbox/kmboxNet.h"
#include <iostream>
#include <windows.h>

// 定义KMBox需要的全局变量
unsigned int xbox_mac = 0;

KMBoxController::K<PERSON>oxController(const KMBoxConfig& config)
    : config_(config), initialized_(false), connected_(false) {
    LogInfo("KMBox Controller created");
}

KMBoxController::~KMBoxController() {
    Disconnect();
}

bool KMBoxController::Initialize() {
    if (initialized_) {
        LogInfo("Already initialized, status: " + std::string(connected_ ? "CONNECTED" : "DISCONNECTED"));
        return connected_;
    }

    LogInfo("Initializing KMBox hardware...");
    LogInfo("Target device: " + config_.ip + ":" + config_.port + " (MAC: " + config_.mac + ")");

    try {
        // 根据官方文档，使用kmNet_init连接设备
        int result = kmNet_init(
            const_cast<char*>(config_.ip.c_str()),
            const_cast<char*>(config_.port.c_str()),
            const_cast<char*>(config_.mac.c_str())
        );

        if (result == 0) {
            initialized_ = true;
            connected_ = true;

            LogInfo("KMBox hardware connected successfully!");
            LogInfo("Encryption: " + std::string(config_.use_encryption ? "ENABLED" : "DISABLED"));
            LogInfo("Smooth move: " + std::string(config_.use_smooth_move ? "ENABLED" : "DISABLED"));

            // 如果需要监控功能，启用监控
            if (config_.use_smooth_move) {
                LogInfo("Starting mouse monitoring...");
                int monitor_result = kmNet_monitor(1000);  // 使用端口1000
                if (monitor_result == 0) {
                    LogInfo("Mouse monitoring started successfully");
                } else {
                    LogError("Failed to start mouse monitoring", monitor_result);
                }
            }

            return true;
        } else {
            LogError("KMBox connection failed", result);
            LogInfo("Please check:");
            LogInfo("  1. Device IP: " + config_.ip);
            LogInfo("  2. Device port: " + config_.port);
            LogInfo("  3. Device MAC: " + config_.mac);
            LogInfo("  4. Network connectivity");
            LogInfo("  5. Windows firewall settings");
            return false;
        }
    } catch (...) {
        LogError("Exception during KMBox initialization");
        return false;
    }
}

void KMBoxController::Disconnect() {
    if (connected_) {
        connected_ = false;
        initialized_ = false;
        LogInfo("KMBox disconnected");
    }
}

void KMBoxController::UpdateConfig(const KMBoxConfig& config) {
    bool need_reconnect = (config.ip != config_.ip ||
                          config.port != config_.port ||
                          config.mac != config_.mac);

    config_ = config;

    if (need_reconnect && initialized_) {
        LogInfo("Configuration changed, reconnecting...");
        Disconnect();
        Initialize();
    }
}

bool KMBoxController::MoveMouse(int dx, int dy) {
    if (!config_.enabled || !connected_) {
        return false;
    }

    if (dx == 0 && dy == 0) {
        return true;
    }

    try {
        int result;
        if (config_.use_encryption) {
            // 使用加密版本的API（推荐）
            result = kmNet_enc_mouse_move(static_cast<short>(dx), static_cast<short>(dy));
        } else {
            // 使用普通版本的API
            result = kmNet_mouse_move(static_cast<short>(dx), static_cast<short>(dy));
        }

        if (result == 0) {
            return true;
        } else {
            LogError("Mouse move failed", result);
            return false;
        }
    } catch (...) {
        LogError("Exception in mouse move");
        return false;
    }
}

bool KMBoxController::MoveMouseSmooth(int dx, int dy, int time_ms) {
    if (!config_.enabled || !connected_) {
        return false;
    }

    if (dx == 0 && dy == 0) {
        return true;
    }

    try {
        int result;
        if (config_.use_encryption) {
            // 使用加密版本的自动平滑移动
            result = kmNet_enc_mouse_move_auto(dx, dy, time_ms);
        } else {
            // 使用普通版本的自动平滑移动
            result = kmNet_mouse_move_auto(dx, dy, time_ms);
        }

        if (result == 0) {
            return true;
        } else {
            LogError("Smooth mouse move failed", result);
            return false;
        }
    } catch (...) {
        LogError("Exception in smooth mouse move");
        return false;
    }
}

bool KMBoxController::MoveMouseAuto(int dx, int dy) {
    return MoveMouseSmooth(dx, dy, config_.smooth_move_time_ms);
}

bool KMBoxController::LeftClick() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result1, result2;
        if (config_.use_encryption) {
            result1 = kmNet_enc_mouse_left(1);  // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_enc_mouse_left(0);  // 松开
        } else {
            result1 = kmNet_mouse_left(1);      // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_mouse_left(0);      // 松开
        }
        return (result1 == 0) && (result2 == 0);
    } catch (...) {
        LogError("Exception in left click");
        return false;
    }
}

bool KMBoxController::LeftDown() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_left(1);
        } else {
            result = kmNet_mouse_left(1);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::LeftUp() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_left(0);
        } else {
            result = kmNet_mouse_left(0);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::RightClick() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result1, result2;
        if (config_.use_encryption) {
            result1 = kmNet_enc_mouse_right(1);  // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_enc_mouse_right(0);  // 松开
        } else {
            result1 = kmNet_mouse_right(1);      // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_mouse_right(0);      // 松开
        }
        return (result1 == 0) && (result2 == 0);
    } catch (...) {
        LogError("Exception in right click");
        return false;
    }
}

bool KMBoxController::RightDown() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_right(1);
        } else {
            result = kmNet_mouse_right(1);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::RightUp() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_right(0);
        } else {
            result = kmNet_mouse_right(0);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::IsRightButtonPressed() {
    if (!connected_) return false;

    try {
        // 使用监控功能检测右键状态
        return kmNet_monitor_mouse_right() == 1;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::IsLeftButtonPressed() {
    if (!connected_) return false;

    try {
        // 使用监控功能检测左键状态
        return kmNet_monitor_mouse_left() == 1;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::TestConnection() {
    if (!connected_) {
        LogInfo("Not connected to KMBox device");
        return false;
    }

    // 测试连接：尝试一个0移动
    try {
        int result = kmNet_mouse_move(0, 0);
        bool success = (result == 0);
        LogInfo("Connection test " + std::string(success ? "PASSED" : "FAILED") + " (code: " + std::to_string(result) + ")");
        return success;
    } catch (...) {
        LogError("Connection test failed with exception");
        return false;
    }
}

void KMBoxController::LogError(const std::string& message, int error_code) {
    if (error_code != 0) {
        std::cout << "[KMBox ERROR] " << message << " (Code: " << error_code << ")" << std::endl;
    } else {
        std::cout << "[KMBox ERROR] " << message << std::endl;
    }
}

void KMBoxController::LogInfo(const std::string& message) {
    std::cout << "[KMBox] " << message << std::endl;
}