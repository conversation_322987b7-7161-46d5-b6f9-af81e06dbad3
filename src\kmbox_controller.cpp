#include "kmbox_controller.h"
#include "kmbox/kmboxNet.h"
#include <iostream>
#include <windows.h>

// 定义KMBox需要的全局变量
unsigned int xbox_mac = 0;

KMBoxController::KMBoxController(const KMBoxConfig& config)
    : config_(config), initialized_(false), connected_(false) {
    LogInfo("KMBox控制器已创建");
}

KMBoxController::~KMBoxController() {
    Disconnect();
}

bool KMBoxController::Initialize() {
    if (initialized_) {
        LogInfo("已初始化，状态: " + std::string(connected_ ? "已连接" : "未连接"));
        return connected_;
    }

    LogInfo("正在初始化KMBox硬件...");
    LogInfo("目标设备: " + config_.ip + ":" + config_.port + " (MAC: " + config_.mac + ")");

    try {
        // 根据官方文档，使用kmNet_init连接设备
        int result = kmNet_init(
            const_cast<char*>(config_.ip.c_str()),
            const_cast<char*>(config_.port.c_str()),
            const_cast<char*>(config_.mac.c_str())
        );

        if (result == 0) {
            initialized_ = true;
            connected_ = true;

            LogInfo("KMBox硬件连接成功！");
            LogInfo("加密传输: " + std::string(config_.use_encryption ? "已启用" : "已禁用"));
            LogInfo("平滑移动: " + std::string(config_.use_smooth_move ? "已启用" : "已禁用"));

            // 如果需要监控功能，启用监控
            if (config_.use_smooth_move) {
                LogInfo("正在启动鼠标监控...");
                int monitor_result = kmNet_monitor(1000);  // 使用端口1000
                if (monitor_result == 0) {
                    LogInfo("鼠标监控启动成功");
                } else {
                    LogError("鼠标监控启动失败", monitor_result);
                }
            }

            return true;
        } else {
            LogError("KMBox连接失败", result);
            LogInfo("请检查:");
            LogInfo("  1. 设备IP: " + config_.ip);
            LogInfo("  2. 设备端口: " + config_.port);
            LogInfo("  3. 设备MAC: " + config_.mac);
            LogInfo("  4. 网络连接");
            LogInfo("  5. Windows防火墙");
            return false;
        }
    } catch (...) {
        LogError("KMBox初始化异常");
        return false;
    }
}

void KMBoxController::Disconnect() {
    if (connected_) {
        connected_ = false;
        initialized_ = false;
        LogInfo("KMBox已断开连接");
    }
}

void KMBoxController::UpdateConfig(const KMBoxConfig& config) {
    bool need_reconnect = (config.ip != config_.ip ||
                          config.port != config_.port ||
                          config.mac != config_.mac);

    config_ = config;

    if (need_reconnect && initialized_) {
        LogInfo("配置已更改，正在重新连接...");
        Disconnect();
        Initialize();
    }
}

bool KMBoxController::MoveMouse(int dx, int dy) {
    if (!config_.enabled || !connected_) {
        return false;
    }

    if (dx == 0 && dy == 0) {
        return true;
    }

    try {
        int result;
        if (config_.use_encryption) {
            // 使用加密版本的API（推荐）
            result = kmNet_enc_mouse_move(static_cast<short>(dx), static_cast<short>(dy));
        } else {
            // 使用普通版本的API
            result = kmNet_mouse_move(static_cast<short>(dx), static_cast<short>(dy));
        }

        if (result == 0) {
            return true;
        } else {
            LogError("鼠标移动失败", result);
            return false;
        }
    } catch (...) {
        LogError("鼠标移动异常");
        return false;
    }
}

bool KMBoxController::MoveMouseSmooth(int dx, int dy, int time_ms) {
    if (!config_.enabled || !connected_) {
        return false;
    }

    if (dx == 0 && dy == 0) {
        return true;
    }

    try {
        int result;
        if (config_.use_encryption) {
            // 使用加密版本的自动平滑移动
            result = kmNet_enc_mouse_move_auto(dx, dy, time_ms);
        } else {
            // 使用普通版本的自动平滑移动
            result = kmNet_mouse_move_auto(dx, dy, time_ms);
        }

        if (result == 0) {
            return true;
        } else {
            LogError("平滑鼠标移动失败", result);
            return false;
        }
    } catch (...) {
        LogError("平滑移动异常");
        return false;
    }
}

bool KMBoxController::MoveMouseAuto(int dx, int dy) {
    return MoveMouseSmooth(dx, dy, config_.smooth_move_time_ms);
}

bool KMBoxController::LeftClick() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result1, result2;
        if (config_.use_encryption) {
            result1 = kmNet_enc_mouse_left(1);  // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_enc_mouse_left(0);  // 松开
        } else {
            result1 = kmNet_mouse_left(1);      // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_mouse_left(0);      // 松开
        }
        return (result1 == 0) && (result2 == 0);
    } catch (...) {
        LogError("左键点击异常");
        return false;
    }
}

bool KMBoxController::LeftDown() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_left(1);
        } else {
            result = kmNet_mouse_left(1);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::LeftUp() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_left(0);
        } else {
            result = kmNet_mouse_left(0);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::RightClick() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result1, result2;
        if (config_.use_encryption) {
            result1 = kmNet_enc_mouse_right(1);  // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_enc_mouse_right(0);  // 松开
        } else {
            result1 = kmNet_mouse_right(1);      // 按下
            Sleep(10);  // 短暂延迟
            result2 = kmNet_mouse_right(0);      // 松开
        }
        return (result1 == 0) && (result2 == 0);
    } catch (...) {
        LogError("右键点击异常");
        return false;
    }
}

bool KMBoxController::RightDown() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_right(1);
        } else {
            result = kmNet_mouse_right(1);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::RightUp() {
    if (!config_.enabled || !connected_) return false;

    try {
        int result;
        if (config_.use_encryption) {
            result = kmNet_enc_mouse_right(0);
        } else {
            result = kmNet_mouse_right(0);
        }
        return result == 0;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::IsRightButtonPressed() {
    if (!connected_) return false;

    try {
        // 使用监控功能检测右键状态
        return kmNet_monitor_mouse_right() == 1;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::IsLeftButtonPressed() {
    if (!connected_) return false;

    try {
        // 使用监控功能检测左键状态
        return kmNet_monitor_mouse_left() == 1;
    } catch (...) {
        return false;
    }
}

bool KMBoxController::TestConnection() {
    if (!connected_) {
        LogInfo("未连接到KMBox设备");
        return false;
    }

    // 测试连接：尝试一个0移动
    try {
        int result = kmNet_mouse_move(0, 0);
        bool success = (result == 0);
        LogInfo("连接测试 " + std::string(success ? "通过" : "失败") + " (代码: " + std::to_string(result) + ")");
        return success;
    } catch (...) {
        LogError("连接测试异常");
        return false;
    }
}

void KMBoxController::LogError(const std::string& message, int error_code) {
    if (error_code != 0) {
        std::cout << "[KMBox ERROR] " << message << " (Code: " << error_code << ")" << std::endl;
    } else {
        std::cout << "[KMBox ERROR] " << message << std::endl;
    }
}

void KMBoxController::LogInfo(const std::string& message) {
    std::cout << "[KMBox] " << message << std::endl;
}