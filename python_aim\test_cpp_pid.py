import numpy as np
import time

try:
    from cpp_pid_controller import PIDController
    print("成功加载C++版PID控制器!")
except ImportError:
    print("未找到C++版PID控制器，回退使用Python版...")
    from pid_controller import PIDController

# 创建PID控制器
pid = PIDController(kp=0.5, ki=0.0, kd=0.05)

# 测试PID参数获取
print("PID参数:", pid.get_gains())

# 测试重置功能
pid.reset()
print("重置后的状态正常")

# 测试计算功能
error = np.array([10.0, -5.0], dtype=np.float64)
print(f"输入误差: {error}")

# 单次计算测试
result = pid.compute(error)
print(f"单次计算输出: {result}")

# 测量性能
print("开始性能测试...")
start_time = time.perf_counter()
iterations = 10000

for _ in range(iterations):
    output = pid.compute(error)
    
end_time = time.perf_counter()
elapsed_time = end_time - start_time

print(f"最终输出: {output}")
print(f"执行{iterations}次计算的总时间: {elapsed_time:.6f}秒")
print(f"每次计算平均时间: {(elapsed_time/iterations)*1000000:.2f}微秒")
print("测试完成!") 