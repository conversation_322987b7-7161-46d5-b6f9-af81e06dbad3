# -*- coding: utf-8 -*-
# 文件名: infer.py
# 功能: ONNX模型推理模块

# --- 导入库 ---
# 导入onnx库，它是一个用来查看和操作ONNX模型结构的工具。
import onnx
# 导入onnxruntime库，并给它取一个简称'ort'。这是真正用来运行ONNX模型，让模型工作的核心库。
import onnxruntime as ort
# 导入NumPy库，并简称'np'。AI模型处理的输入（图片）和输出（结果）都是以NumPy数组的形式存在的。
import numpy as np
# 导入OpenCV库，并简称'cv2'。我们用它来读取和处理图片。
import cv2
# 导入time库，用来测量推理所需的时间。
import time
# 导入typing库中的Tuple，用于更准确地指定变量类型。
from typing import Tuple

# --- 定义推理类 ---
# 定义一个名为'ONNXInference'的类，它包含了加载模型、处理图片和执行推理的所有功能。
# 把它想象成一个拥有AI大脑的机器人。
class ONNXInference:
    """
    ONNX模型推理类 (ONNXInference)

    这个类的任务是操作一个ONNX格式的AI模型。它会加载模型，
    把输入的图片处理成模型能看懂的格式，然后让模型进行"思考"（推理），
    最后给出模型"思考"的结果。
    """

    # --- 初始化方法 ---
    # 创建'ONNXInference'对象时，这个方法会被自动调用。
    def __init__(self, model_path: str) -> None:
        """
        初始化AI推理机器人。

        参数 (Parameters):
            model_path (str): 你的ONNX模型文件存放在哪里的路径。
            input_size (Tuple[int, int]): 模型要求输入的图片尺寸，默认是320x320像素。
                                          你需要根据你的模型进行修改。
        """
        # --- 保存初始设置 ---
        print("--- 初始化AI推理引擎 ---")
        # 把模型路径存到对象的属性'self.model_path'中，方便以后使用。
        self.model_path = model_path
        # 把模型需要的图片尺寸存到'self.input_size'中。
        
        
        # --- 初始化内部变量 ---
        # 在这里先创建一些空的变量，之后会填入内容。
        self.model = None          # 用来存放加载后的ONNX模型结构。
        self.session = None        # 用来存放ONNX Runtime的推理会话，这是执行推理的核心。
        self.input_name = None     # 用来存模型输入节点的名字。
        self.output_name = None    # 用来存模型输出节点的名字。

        # --- 执行初始化步骤 ---
        # 按照顺序调用内部的私有方法，完成准备工作。
        # 通常以'_'开头的方法被认为是'私有的'，意思是它们主要在类的内部使用。
        self._load_model()          # 第一步：加载模型文件。
        self._initialize_session()  # 第二步：创建推理会s话。
        self.input_size = self.get_input_size()

        self._print_model_info()    # 第三步：打印模型信息。
        print("--- AI推理引擎初始化完毕 ---")
    
    # --- 加载模型方法 --- 
    def _load_model(self) -> None:
        """
        私有方法：加载ONNX模型文件到内存中。
        """
        # 'try...except'结构用来捕获可能发生的错误。如果'try'里的代码出错了，
        # 程序不会直接崩溃，而是会执行'except'里的代码。
        try:
            # 打印提示信息，告诉用户我们正在做什么。
            print(f"正在从路径加载模型: {self.model_path}")
            # 使用onnx.load()函数读取模型文件，并把它存入'self.model'。
            self.model = onnx.load(self.model_path)
            # 打印成功信息。
            print("模型加载成功！")
        except Exception as e:
            # 如果加载过程中发生任何错误（比如文件不存在、文件损坏），
            # 就会执行这里的代码。'e'是具体的错误信息。
            print(f"错误：加载模型失败！原因: {e}")
            # 'raise'会把这个错误再次抛出，让整个程序停止，因为模型加载失败，后续无法进行。
            raise
    
    # --- 初始化会话方法 ---
    def _initialize_session(self) -> None:
        """
        私有方法：创建ONNX Runtime的推理会话(Session)。
        """
        try:
            # --- 配置会话选项 ---
            # 创建一个会话配置对象，我们可以用它来设置推理时的一些高级选项。
            session_options = ort.SessionOptions()
            # session_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

            # --- 动态选择最佳运行设备 ---
            available_providers = ort.get_available_providers()
            print(f"可用的运行设备: {available_providers}")
            
            preferred_providers = ['DmlExecutionProvider', 'CUDAExecutionProvider', 'CPUExecutionProvider']
            
            providers_to_use = []
            for p in preferred_providers:
                if p in available_providers:
                    providers_to_use.append(p)
            
            if not providers_to_use:
                raise RuntimeError("没有可用的ONNX Runtime provider，甚至CPU都不支持？")

            print(f"正在创建推理会话，将按此优先级顺序尝试: {providers_to_use}")

            # --- 创建会话 ---
            # 这是最关键的一步，我们创建了一个InferenceSession（推理会话）的实例。
            self.session = ort.InferenceSession(
                self.model.SerializeToString(), # 参数1: 模型数据，我们把加载的模型转换成二进制字符串给它。
                sess_options=session_options,   # 参数2: 刚才的配置选项。
                providers=providers_to_use      # 参数3: 指定硬件列表。
            )
            print(f"成功使用 '{self.session.get_providers()[0]}' 创建推理会话！")

        except Exception as e:
            # 如果创建会话失败，直接报错并终止程序。
            print(f"错误：创建ONNX Runtime会话失败！原因: {e}")
            raise

        # --- 获取输入输出名称 ---
        # 会话创建成功后，我们要从模型里获取输入和输出"节点"的名字。
        # 这样在推理时，我们才知道要把数据喂给哪个"入口"，并从哪个"出口"取结果。
        # get_inputs()返回一个列表，[0]代表第一个输入节点。
        self.input_name = self.session.get_inputs()[0].name
        # get_outputs()返回一个列表，[0]代表第一个输出节点。
        self.output_name = self.session.get_outputs()[0].name

    # --- 打印模型信息方法 ---
    def _print_model_info(self) -> None:
        """
        私有方法：打印模型的输入和输出信息，方便我们了解模型。
        """
        # 获取输入节点的信息。
        input_node = self.session.get_inputs()[0]
        # 打印一个标题。
        print("\n===== 模型输入信息 =====")
        # 打印输入节点的名称。
        print(f"  节点名称: {input_node.name}")
        # 打印输入节点的形状。比如[1, 3, 320, 320]代表(批次数, 通道数, 高, 宽)。
        print(f"  输入形状: {input_node.shape}")
        # 打印输入节点的数据类型，比如'tensor(float)'。
        print(f"  数据类型: {input_node.type}")

        # 获取输出节点的信息。
        output_node = self.session.get_outputs()[0]
        # 打印一个标题。
        print("\n===== 模型输出信息 =====")
        # 打印输出节点的名称。
        print(f"  节点名称: {output_node.name}")
        # 打印输出节点的形状。比如[1, 6300, 7]代表(批次数, 检测框数, 每个框的数据)。
        print(f"  输出形状: {output_node.shape}")
        # 打印输出节点的数据类型。
        print(f"  数据类型: {output_node.type}")
        print("=" * 26 + "\n") # 打印分割线

    # --- 图像预处理方法 ---
    def preprocess(self, image: np.ndarray) -> np.ndarray:
        """
        把一张普通的图片，处理成AI模型能"吃"的格式。

        参数 (Parameters):
            image (np.ndarray): 一张用OpenCV读取的BGR格式的图片。

        返回 (Returns):
            np.ndarray: 返回一个处理好的，符合模型输入要求的NumPy数组。
        """
        # --- 步骤1: 调整图片尺寸 ---
        # 使用cv2.resize()函数，把图片统一缩放或拉伸到模型需要的尺寸（比如320x320）。
        # resized_image = cv2.resize(image, tuple(self.input_size))

        # # --- 步骤2: 转换颜色通道 ---
        # # OpenCV默认的颜色顺序是BGR（蓝绿红），但大多数AI模型训练时用的是RGB（红绿蓝）。
        # # 我们用cv2.cvtColor()把颜色顺序调换一下。
        # rgb_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)

        # # --- 步骤3: 调整维度顺序 ---
        # # 图片的NumPy数组原始维度是(高, 宽, 通道数)，即HWC格式。
        # # 但大多数AI框架使用(通道数, 高, 宽)，即CHW格式。
        # # 我们用.transpose(2, 0, 1)来交换维度的顺序。
        # transposed_image = rgb_image.transpose(2, 0, 1)

        # # --- 步骤4: 归一化 ---
        # # 把像素的数值从0到255的整数范围，转换到0.0到1.0的浮点数范围。
        # # 这是通过除以255.0来实现的。归一化能让模型训练得更稳定，效果更好。
        # # 我们先用.astype(np.float32)把数据类型变成浮点数，再做除法。
        # normalized_image = transposed_image.astype(np.float32) / 255.0

        # # --- 步骤5: 增加批次维度 ---
        # # 模型通常被设计成可以一次处理一批图片。所以即使我们只有一张图，
        # # 也要在最前面增加一个"批次"维度，把它变成(1, 通道数, 高, 宽)的形状。
        # # np.expand_dims()函数可以帮我们增加这个维度。
        # batched_image = np.expand_dims(normalized_image, axis=0)

        # # 返回最终处理好的图像数据。
        # return batched_image
        #链式操作减少中间变量，性能更高的写法
        return np.expand_dims(
        cv2.cvtColor(
            cv2.resize(image, tuple(self.input_size)), 
            cv2.COLOR_BGR2RGB
        ).transpose(2, 0, 1).astype(np.float32) / 255.0,
        axis=0
        )
    

    # --- 推理方法 ---
    def inference(self, image: np.ndarray) -> np.ndarray:
        """
        执行核心的AI推理过程。

        参数 (Parameters):
            image (np.ndarray): 一张原始的BGR格式图片。

        返回 (Returns):
            np.ndarray: 返回模型推理后的原始输出结果数组。
        """
        try:
            # --- 步骤1: 图像预处理 ---
            # 调用我们上面写好的preprocess方法，把图片处理好。
            input_tensor = self.preprocess(image)

            # --- 步骤2: 执行推理 ---
            # 调用会话的run()方法来真正运行模型。
            # 这是与AI模型交互最核心的一步。
            outputs = self.session.run(
                [self.output_name],         # 参数1: 我们想从哪个输出节点取结果，需要一个列表。
                {self.input_name: input_tensor} # 参数2: 我们要喂给模型什么数据，需要一个字典，
                                                # key是输入节点的名字，value是处理好的图片数据。
            )

            # --- 步骤3: 返回结果 ---
            # run()方法返回的是一个列表，因为模型可能有多个输出。
            # 在我们的例子里，只有一个输出，所以我们用[0]把它取出来。
            # 输出了6300个检测框，每个检测框有7个数据，分别是：
            # 1. 框的中心点x坐标
            # 2. 框的中心点y坐标
            # 3. 框的宽度
            # 4. 框的高度
            # 5. 框的置信度
            # 6. 框的类别
            return outputs[0]

        except Exception as e:
            # 如果推理过程中出错，打印错误信息。
            print(f"错误：模型推理失败！原因: {e}")
            raise
    def get_input_size(self):
        return self.session.get_inputs()[0].shape[2:]
    def get_output_size(self):
        return self.session.get_outputs()[0].shape[2:]
# --- 程序主入口 ---
if __name__ == "__main__":
    
    print("--- 这是一个测试程序，将加载模型并对一张图片进行推理 ---")
    
    # --- 创建对象 ---
    # 创建ONNXInference类的实例，取名'inferencer'。
    # 把模型路径和需要的输入尺寸告诉它。
    inferencer = ONNXInference(
        model_path="1.onnx"
    )

    # --- 读取测试图片 ---
    # 使用cv2.imread()读取一张名为"1.jpg"的图片。
    print("正在读取测试图片 '1.jpg' ...")
    test_image = cv2.imread("1.jpg")

    # --- 检查图片是否读取成功 ---
    # 如果图片不存在或无法读取，cv2.imread()会返回None。
    if test_image is None:
        print("错误：无法读取测试图片'1.jpg'，请确保文件在同一个目录下！")
    else:
        print("测试图片读取成功！")
        # --- 执行推理 ---
        # 记录开始时间。
        start_time = time.perf_counter()
        # 调用对象的inference()方法，对图片进行推理。
        output = inferencer.inference(test_image)

        # 记录结束时间。
        end_time = time.perf_counter()

        # --- 打印结果 ---
        # 计算并打印推理花费的时间（毫秒）。
        print(f"\n推理耗时: {(end_time - start_time) * 1000:.2f} 毫秒")
        # 打印模型输出结果的形状。
        print(f"模型输出的形状: {output.shape}")

        # --- 解析并显示部分结果 ---
        # 检查输出结果是不是一个三维数组，这通常是目标检测模型的输出格式。
        if len(output.shape) == 3:
            # np.squeeze()可以把数组中尺寸为1的维度去掉，方便处理。
            # 比如(1, 6300, 7)会变成(6300, 7)。
            predictions = np.squeeze(output)
            print(f"\n模型共产生了 {predictions.shape[0]} 个潜在的检测结果。")
            print("只显示其中置信度大于0.5的结果：")
            
            # 遍历所有的检测结果。
            for i in range(predictions.shape[0]):
                # pred[4]通常是这个检测框的置信度分数。
                confidence = predictions[i][4]
                # 只打印那些我们比较确信的结果。
                if confidence > 0.5:
                    # 打印这个检测结果的详细数据。
                    print(f"  - 检测 #{i+1} (置信度: {confidence:.2f}): {predictions[i]}")
    
    print("\n--- 测试程序结束 ---")




   