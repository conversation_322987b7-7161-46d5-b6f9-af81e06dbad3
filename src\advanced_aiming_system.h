#pragma once

#include "pid_controller.h"
#include "kmbox_controller.h"
#include <vector>
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>

// 前向声明
struct Detection;

// 高级自瞄系统配置
struct AdvancedAimingConfig {
    // 目标选择参数
    float aim_circle_radius = 180.0f;   // 自瞄圆环半径
    int primary_class = 0;              // 主要目标类别 (0=头部)
    int secondary_class = 1;            // 次要目标类别 (1=身体)
    float head_offset_y = 0.13f;        // 头部瞄准偏移
    float body_offset_y = 0.5f;         // 身体瞄准偏移
    
    // 屏幕参数
    int screen_center_x = 160;          // 屏幕中心X
    int screen_center_y = 160;          // 屏幕中心Y
    
    // PID控制参数
    PIDConfig pid_config;               // PID控制器配置
    
    // KMBox参数
    KMBoxConfig kmbox_config;           // KMBox配置
    
    // 系统参数
    bool enabled = false;               // 是否启用自瞄
    bool use_hardware_mouse = true;     // 优先使用硬件鼠标
    bool debug_output = false;          // 是否输出调试信息
    float update_frequency = 200.0f;    // 更新频率 (Hz)
    
    // 目标跟踪参数
    float target_switch_threshold = 50.0f;  // 目标切换阈值
    int target_lost_timeout = 5;            // 目标丢失超时帧数
    bool enable_target_prediction = true;   // 启用目标预测
};

// 自瞄统计信息
struct AimingStats {
    int total_detections = 0;           // 总检测数
    int valid_targets = 0;              // 有效目标数
    int successful_aims = 0;            // 成功自瞄次数
    float average_error = 0.0f;         // 平均误差
    float average_response_time = 0.0f; // 平均响应时间
    bool hardware_mouse_active = false; // 硬件鼠标是否激活
    std::string current_target_type;    // 当前目标类型
};

// 高级自瞄系统类
class AdvancedAimingSystem {
private:
    AdvancedAimingConfig config_;
    
    // 核心组件
    std::unique_ptr<PIDController> pid_controller_;
    std::unique_ptr<KMBoxController> kmbox_controller_;
    
    // 系统状态
    std::atomic<bool> enabled_;
    std::atomic<bool> running_;
    bool hardware_mouse_available_;
    
    // 目标跟踪
    Detection current_target_;
    bool has_valid_target_;
    int target_lost_frames_;
    std::chrono::steady_clock::time_point last_target_time_;
    
    // 统计信息
    AimingStats stats_;
    std::mutex stats_mutex_;
    
    // 线程安全
    std::mutex target_mutex_;

public:
    AdvancedAimingSystem(const AdvancedAimingConfig& config = AdvancedAimingConfig{});
    ~AdvancedAimingSystem();

    // 系统管理
    bool Initialize();                  // 初始化系统
    void Shutdown();                    // 关闭系统
    bool IsInitialized() const;         // 检查是否已初始化
    
    // 配置管理
    void UpdateConfig(const AdvancedAimingConfig& config);
    AdvancedAimingConfig GetConfig() const;
    
    // 控制接口
    void SetEnabled(bool enabled);      // 启用/禁用自瞄
    bool IsEnabled() const;             // 检查是否启用
    
    // 主要处理接口
    void ProcessDetections(const std::vector<Detection>& detections);
    
    // 手动控制接口
    bool IsAimKeyPressed();             // 检查自瞄键是否按下
    void ForceAimAt(float x, float y);  // 强制瞄准指定位置
    
    // 统计信息
    AimingStats GetStats() const;       // 获取统计信息
    void ResetStats();                  // 重置统计信息
    
    // 调试接口
    PIDController::DebugInfo GetPIDDebugInfo() const;
    void SetDebugOutput(bool enabled);  // 启用/禁用调试输出

private:
    // 目标选择算法
    Detection SelectBestTarget(const std::vector<Detection>& detections);
    bool IsTargetInAimCircle(const Detection& detection);
    float CalculateTargetDistance(const Detection& detection);
    float CalculateTargetPriority(const Detection& detection);
    
    // 目标处理
    void UpdateCurrentTarget(const Detection& target);
    void HandleTargetLost();
    bool ShouldSwitchTarget(const Detection& new_target);
    
    // 瞄准计算
    std::array<float, 2> CalculateAimPoint(const Detection& target);
    std::array<float, 2> ApplyTargetOffset(const Detection& target);
    
    // 鼠标控制
    bool ExecuteMouseMove(float dx, float dy);
    bool TryHardwareMouseMove(float dx, float dy);
    bool TrySystemMouseMove(float dx, float dy);
    
    // 统计更新
    void UpdateStats(const std::vector<Detection>& detections, bool aim_executed);
    
    // 调试输出
    void DebugPrint(const std::string& message);
    void PrintTargetInfo(const Detection& target);
    void PrintPIDInfo(const PIDController::DebugInfo& info);
    
    // 工具函数
    std::string GetTargetTypeName(int class_id);
    float GetCurrentTime();
};
