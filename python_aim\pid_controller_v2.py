import numpy as np
import time

class PIDControllerV2:
    """
    一个为固定时间步长（Fixed Delta Time）设计的PID控制器。
    包含了自适应积分（积分分离）和微分先行（Derivative on Measurement）等高级功能，
    以实现更平滑、更稳定的控制效果。
    """
    def __init__(self, 
                 kp=1.0, ki=1.0, kd=1.0, 
                 sample_time=0.004,
                 base_ki=0.0, max_ki=10.0,
                 inner_threshold=5.0, outer_threshold=30.0):
        """
        初始化PID控制器。

        参数:
            kp, ki, kd (float): PID增益系数。
            sample_time (float): 固定的采样时间/时间步长 (dt)，单位为秒。
            base_ki (float): 基础积分增益（大误差时）。
            max_ki (float): 最大积分增益（小误差时）。
            inner_threshold (float): 内阈值，误差小于此值时使用最大积分增益。
            outer_threshold (float): 外阈值，误差大于此值时禁用积分。
        """
        self.kp = kp
        self.ki = ki  # ki在此作为最大值的参考，实际使用的是 base_ki 和 max_ki
        self.kd = kd
        
        self.sample_time = sample_time
        
        # 自适应积分（积分分离）参数
        self.base_ki = base_ki
        self.max_ki = max_ki
        self.inner_threshold = inner_threshold
        self.outer_threshold = outer_threshold

        self._p_term = np.array([0.0, 0.0])
        self._i_term = np.array([0.0, 0.0])
        self._d_term = np.array([0.0, 0.0])
        
        self._last_error = np.array([0.0, 0.0])
        
        # 微分先行（Derivative on Measurement）的额外状态
        # 我们对输入的变化率进行微分，而不是对误差的变化率
        self._last_measurement = np.array([0.0, 0.0])

        self.reset()

    def compute(self, error, measurement):
        """
        根据新的误差和测量值计算PID输出。
        
        参数:
            error (np.array): 当前的误差向量 (目标 - 当前)。
            measurement (np.array): 当前的测量值（例如，剩余误差）。
                                     在我们的模型中，error和measurement是同一个值。

        返回:
            np.array: 控制器输出的移动向量。
        """
        # 1. 比例项 (Proportional)
        self._p_term = self.kp * error

        # 2. 积分项 (Integral) with Adaptive KI (积分分离)
        current_ki = self._get_adaptive_ki(error)
        self._i_term += current_ki * error * self.sample_time
        
        # 积分饱和限制 (Integral Windup)
        # 在这里我们可以增加一个限制，防止积分项无限增大，但暂时保持简单
        
        # 3. 微分项 (Derivative) on Measurement
        # 这种方法可以避免在目标值突变时（比如切换目标）引起的微分尖峰。
        derivative = (measurement - self._last_measurement) / self.sample_time
        self._d_term = self.kd * derivative
        
        # 更新历史值
        self._last_error = error
        self._last_measurement = measurement

        # 最终输出 = P + I - D
        # 我们从P和I中减去D，因为D项代表的是阻尼力，与运动方向相反
        output = self._p_term + self._i_term - self._d_term
        
        return output

    def _get_adaptive_ki(self, error):
        """根据误差大小动态调整积分系数 Ki (积分分离)"""
        error_magnitude = np.linalg.norm(error) # 计算误差向量的长度
        
        if error_magnitude < self.inner_threshold:
            # 在目标附近，使用最大积分，消除静差
            return self.max_ki
        elif error_magnitude > self.outer_threshold:
            # 距离目标很远，禁用积分，防止积分过饱和
            return 0.0
        else:
            # 在中间区域，使用基础积分
            return self.base_ki

    def reset(self):
        """重置PID控制器的内部状态。"""
        self._p_term = np.array([0.0, 0.0])
        self._i_term = np.array([0.0, 0.0])
        self._d_term = np.array([0.0, 0.0])
        self._last_error = np.array([0.0, 0.0])
        self._last_measurement = np.array([0.0, 0.0])

    def set_gains(self, kp, ki, kd, base_ki, max_ki):
        """动态调整PID增益。"""
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.base_ki = base_ki
        self.max_ki = max_ki
        self.reset()

    def set_thresholds(self, inner, outer):
        """动态调整积分分离的阈值。"""
        self.inner_threshold = inner
        self.outer_threshold = outer
        self.reset()
        
    def get_gains(self):
        """获取当前PID增益"""
        return {"kp": self.kp, "ki": self.ki, "kd": self.kd}
        
    def get_current_ki(self, error):
        """获取基于当前误差的自适应Ki值"""
        return self._get_adaptive_ki(error) 