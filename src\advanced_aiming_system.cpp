#include "advanced_aiming_system.h"
#include "inference_engine.h"  // ??????Detection????
#include <iostream>
#include <cmath>
#include <algorithm>
#include <windows.h>

AdvancedAimingSystem::AdvancedAimingSystem(const AdvancedAimingConfig& config) 
    : config_(config), enabled_(false), running_(false), hardware_mouse_available_(false),
      has_valid_target_(false), target_lost_frames_(0) {
    
    std::cout << "[???????] ??????????..." << std::endl;
    
    // ???????????
    current_target_ = {};
    current_target_.classId = -1;  // ????????
    
    // ??????????
    ResetStats();
}

AdvancedAimingSystem::~AdvancedAimingSystem() {
    Shutdown();
}

bool AdvancedAimingSystem::Initialize() {
    std::cout << "[???????] ????????..." << std::endl;
    
    try {
        // ?????PID??????
        pid_controller_ = std::make_unique<PIDController>(config_.pid_config);
        if (!pid_controller_) {
            std::cout << "[???????] PID?????????????" << std::endl;
            return false;
        }
        
        // ?????KMBox??????
        if (config_.use_hardware_mouse) {
            kmbox_controller_ = std::make_unique<KMBoxController>(config_.kmbox_config);
            if (kmbox_controller_) {
                hardware_mouse_available_ = kmbox_controller_->Initialize();
                if (hardware_mouse_available_) {
                    std::cout << "[???????] ???????????????????" << std::endl;
                } else {
                    std::cout << "[???????] ??????????????????????????????" << std::endl;
                }
            }
        }
        
        running_ = true;
        
        std::cout << "[???????] ????????" << std::endl;
        std::cout << "[???????] PID????: Kp=" << config_.pid_config.kp 
                  << ", Ki=" << config_.pid_config.ki << ", Kd=" << config_.pid_config.kd << std::endl;
        std::cout << "[???????] ?????????: " << config_.aim_circle_radius << std::endl;
        std::cout << "[???????] ??????: " << (hardware_mouse_available_ ? "????" : "??????") << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[???????] ???????: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[???????] ??????????????" << std::endl;
        return false;
    }
}

void AdvancedAimingSystem::Shutdown() {
    if (!running_) return;
    
    std::cout << "[???????] ??????..." << std::endl;
    
    running_ = false;
    enabled_ = false;
    
    // ???KMBox??????
    if (kmbox_controller_) {
        kmbox_controller_->Disconnect();
        kmbox_controller_.reset();
    }
    
    // ????PID??????
    if (pid_controller_) {
        pid_controller_->Reset();
        pid_controller_.reset();
    }
    
    hardware_mouse_available_ = false;
    has_valid_target_ = false;
    
    std::cout << "[???????] ????" << std::endl;
}

bool AdvancedAimingSystem::IsInitialized() const {
    return running_ && pid_controller_ != nullptr;
}

void AdvancedAimingSystem::UpdateConfig(const AdvancedAimingConfig& config) {
    config_ = config;
    
    if (pid_controller_) {
        pid_controller_->UpdateConfig(config_.pid_config);
    }
    
    if (kmbox_controller_) {
        kmbox_controller_->UpdateConfig(config_.kmbox_config);
    }
    
    std::cout << "[???????] ?????????" << std::endl;
}

AdvancedAimingConfig AdvancedAimingSystem::GetConfig() const {
    return config_;
}

void AdvancedAimingSystem::SetEnabled(bool enabled) {
    enabled_ = enabled;
    
    if (pid_controller_) {
        pid_controller_->SetEnabled(enabled);
    }
    
    if (enabled) {
        std::cout << "[???????] ??????" << std::endl;
    } else {
        std::cout << "[???????] ?????" << std::endl;
        HandleTargetLost();  // ?????????
    }
}

bool AdvancedAimingSystem::IsEnabled() const {
    return enabled_ && running_;
}

void AdvancedAimingSystem::ProcessDetections(const std::vector<Detection>& detections) {
    if (!IsEnabled() || !IsInitialized()) {
        return;
    }
    
    // ?????????????
    if (!IsAimKeyPressed()) {
        HandleTargetLost();
        return;
    }
    
    // ?????????
    Detection best_target = SelectBestTarget(detections);
    
    if (best_target.classId >= 0) {
        // ??????????
        UpdateCurrentTarget(best_target);
        
        // ?????????
        auto aim_point = CalculateAimPoint(best_target);
        
        // ???PID???????????????
        auto pid_output = pid_controller_->Update(
            aim_point[0], aim_point[1], 
            static_cast<float>(config_.screen_center_x), 
            static_cast<float>(config_.screen_center_y)
        );
        
        // ?????????
        bool move_success = ExecuteMouseMove(pid_output[0], pid_output[1]);
        
        // ??????????
        UpdateStats(detections, move_success);
        
        // ???????
        if (config_.debug_output) {
            PrintTargetInfo(best_target);
            PrintPIDInfo(pid_controller_->GetDebugInfo());
        }
        
    } else {
        // ?????????????
        HandleTargetLost();
        UpdateStats(detections, false);
    }
}

bool AdvancedAimingSystem::IsAimKeyPressed() {
    // ????????? (0x02)
    return (GetAsyncKeyState(0x02) & 0x8000) != 0;
}

void AdvancedAimingSystem::ForceAimAt(float x, float y) {
    if (!IsEnabled() || !IsInitialized()) {
        return;
    }
    
    auto pid_output = pid_controller_->Update(
        x, y,
        static_cast<float>(config_.screen_center_x),
        static_cast<float>(config_.screen_center_y)
    );
    
    ExecuteMouseMove(pid_output[0], pid_output[1]);
}

AimingStats AdvancedAimingSystem::GetStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void AdvancedAimingSystem::ResetStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = {};
    stats_.hardware_mouse_active = hardware_mouse_available_;
}

PIDController::DebugInfo AdvancedAimingSystem::GetPIDDebugInfo() const {
    if (pid_controller_) {
        return pid_controller_->GetDebugInfo();
    }
    return {};
}

void AdvancedAimingSystem::SetDebugOutput(bool enabled) {
    config_.debug_output = enabled;
}

Detection AdvancedAimingSystem::SelectBestTarget(const std::vector<Detection>& detections) {
    if (detections.empty()) {
        Detection invalid_target = {};
        invalid_target.classId = -1;
        return invalid_target;
    }
    
    std::vector<Detection> valid_targets;
    
    // ?????????????????
    for (const auto& det : detections) {
        if (IsTargetInAimCircle(det)) {
            valid_targets.push_back(det);
        }
    }
    
    if (valid_targets.empty()) {
        Detection invalid_target = {};
        invalid_target.classId = -1;
        return invalid_target;
    }
    
    // ???????????
    std::sort(valid_targets.begin(), valid_targets.end(), 
              [this](const Detection& a, const Detection& b) {
                  return CalculateTargetPriority(a) > CalculateTargetPriority(b);
              });
    
    return valid_targets[0];
}

bool AdvancedAimingSystem::IsTargetInAimCircle(const Detection& detection) {
    float distance = CalculateTargetDistance(detection);
    return distance <= config_.aim_circle_radius;
}

float AdvancedAimingSystem::CalculateTargetDistance(const Detection& detection) {
    float center_x = detection.x + detection.width / 2.0f;
    float center_y = detection.y + detection.height / 2.0f;
    
    float dx = center_x - config_.screen_center_x;
    float dy = center_y - config_.screen_center_y;
    
    return std::sqrt(dx * dx + dy * dy);
}

float AdvancedAimingSystem::CalculateTargetPriority(const Detection& detection) {
    float priority = 0.0f;
    
    // ????????
    if (detection.classId == config_.primary_class) {
        priority += 100.0f;
    } else if (detection.classId == config_.secondary_class) {
        priority += 50.0f;
    }
    
    // ????????? (???????????????)
    float distance = CalculateTargetDistance(detection);
    priority += (config_.aim_circle_radius - distance) / config_.aim_circle_radius * 50.0f;
    
    // ??????????
    priority += detection.confidence * 20.0f;
    
    return priority;
}

void AdvancedAimingSystem::UpdateCurrentTarget(const Detection& target) {
    std::lock_guard<std::mutex> lock(target_mutex_);

    current_target_ = target;
    has_valid_target_ = true;
    target_lost_frames_ = 0;
    last_target_time_ = std::chrono::steady_clock::now();

    // ????PID?????????????
    if (pid_controller_) {
        float center_x = target.x + target.width / 2.0f;
        float center_y = target.y + target.height / 2.0f;
        pid_controller_->UpdateTargetState(center_x, center_y);
    }
}

void AdvancedAimingSystem::HandleTargetLost() {
    std::lock_guard<std::mutex> lock(target_mutex_);

    target_lost_frames_++;

    if (target_lost_frames_ >= config_.target_lost_timeout) {
        has_valid_target_ = false;
        current_target_.classId = -1;

        if (pid_controller_) {
            pid_controller_->SetTargetLost();
        }
    }
}

bool AdvancedAimingSystem::ShouldSwitchTarget(const Detection& new_target) {
    if (!has_valid_target_) {
        return true;
    }

    float current_priority = CalculateTargetPriority(current_target_);
    float new_priority = CalculateTargetPriority(new_target);

    return new_priority > current_priority + config_.target_switch_threshold;
}

std::array<float, 2> AdvancedAimingSystem::CalculateAimPoint(const Detection& target) {
    // ????????????
    float center_x = target.x + target.width / 2.0f;
    float center_y = target.y + target.height / 2.0f;

    // ?????????
    auto offset = ApplyTargetOffset(target);

    return {center_x + offset[0], center_y + offset[1]};
}

std::array<float, 2> AdvancedAimingSystem::ApplyTargetOffset(const Detection& target) {
    float offset_x = 0.0f;
    float offset_y = 0.0f;

    if (target.classId == 0) {
        // ?????????????
        offset_y = -target.height * (0.5f - config_.head_offset_y);
    } else if (target.classId == 1) {
        // ??????????????
        offset_y = -target.height * (0.5f - config_.body_offset_y);
    }

    return {offset_x, offset_y};
}

bool AdvancedAimingSystem::ExecuteMouseMove(float dx, float dy) {
    if (std::abs(dx) < 0.1f && std::abs(dy) < 0.1f) {
        return true;  // ??????????????
    }

    // ?????????????
    if (hardware_mouse_available_ && TryHardwareMouseMove(dx, dy)) {
        return true;
    }

    // ??????????
    return TrySystemMouseMove(dx, dy);
}

bool AdvancedAimingSystem::TryHardwareMouseMove(float dx, float dy) {
    if (!kmbox_controller_ || !kmbox_controller_->IsEnabled()) {
        return false;
    }

    int move_x = static_cast<int>(std::round(dx));
    int move_y = static_cast<int>(std::round(dy));

    return kmbox_controller_->MoveMouseAuto(move_x, move_y);
}

bool AdvancedAimingSystem::TrySystemMouseMove(float dx, float dy) {
    try {
        POINT current_pos;
        if (!GetCursorPos(&current_pos)) {
            return false;
        }

        int new_x = current_pos.x + static_cast<int>(std::round(dx));
        int new_y = current_pos.y + static_cast<int>(std::round(dy));

        return SetCursorPos(new_x, new_y) != 0;
    } catch (...) {
        return false;
    }
}

void AdvancedAimingSystem::UpdateStats(const std::vector<Detection>& detections, bool aim_executed) {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    stats_.total_detections += static_cast<int>(detections.size());

    if (has_valid_target_) {
        stats_.valid_targets++;
        stats_.current_target_type = GetTargetTypeName(current_target_.classId);

        if (aim_executed) {
            stats_.successful_aims++;
        }

        // ??????????
        if (pid_controller_) {
            auto debug_info = pid_controller_->GetDebugInfo();
            float current_error = std::sqrt(debug_info.error_x * debug_info.error_x +
                                          debug_info.error_y * debug_info.error_y);
            stats_.average_error = (stats_.average_error * 0.9f) + (current_error * 0.1f);
        }
    } else {
        stats_.current_target_type = "?????";
    }

    stats_.hardware_mouse_active = hardware_mouse_available_ &&
                                   kmbox_controller_ &&
                                   kmbox_controller_->IsEnabled();
}

void AdvancedAimingSystem::DebugPrint(const std::string& message) {
    if (config_.debug_output) {
        std::cout << "[??????????] " << message << std::endl;
    }
}

void AdvancedAimingSystem::PrintTargetInfo(const Detection& target) {
    std::cout << "[??????] ????=" << GetTargetTypeName(target.classId)
              << ", ????=(" << static_cast<int>(target.x) << "," << static_cast<int>(target.y) << ")"
              << ", ???=" << static_cast<int>(target.width) << "x" << static_cast<int>(target.height)
              << ", ?????=" << target.confidence << std::endl;
}

void AdvancedAimingSystem::PrintPIDInfo(const PIDController::DebugInfo& info) {
    std::cout << "[PID???] ???=(" << static_cast<int>(info.error_x) << "," << static_cast<int>(info.error_y) << ")"
              << ", ???=(" << static_cast<int>(info.output_x) << "," << static_cast<int>(info.output_y) << ")"
              << ", ????=" << static_cast<int>(info.distance)
              << ", Kp=" << info.adaptive_kp << std::endl;
}

std::string AdvancedAimingSystem::GetTargetTypeName(int class_id) {
    switch (class_id) {
        case 0: return "???";
        case 1: return "????";
        default: return "???";
    }
}
















