#include "advanced_aiming_system.h"
#include "inference_engine.h"  // 为了使用Detection结构体
#include <iostream>
#include <cmath>
#include <algorithm>
#include <windows.h>

AdvancedAimingSystem::AdvancedAimingSystem(const AdvancedAimingConfig& config) 
    : config_(config), enabled_(false), running_(false), hardware_mouse_available_(false),
      has_valid_target_(false), target_lost_frames_(0) {
    
    std::cout << "[高级自瞄] 创建自瞄系统..." << std::endl;
    
    // 初始化当前目标
    current_target_ = {};
    current_target_.classId = -1;  // 标记为无效
    
    // 重置统计信息
    ResetStats();
}

AdvancedAimingSystem::~AdvancedAimingSystem() {
    Shutdown();
}

bool AdvancedAimingSystem::Initialize() {
    std::cout << "[高级自瞄] 正在初始化..." << std::endl;
    
    try {
        // 初始化PID控制器
        pid_controller_ = std::make_unique<PIDController>(config_.pid_config);
        if (!pid_controller_) {
            std::cout << "[高级自瞄] PID控制器创建失败" << std::endl;
            return false;
        }
        
        // 初始化KMBox控制器
        if (config_.use_hardware_mouse) {
            kmbox_controller_ = std::make_unique<KMBoxController>(config_.kmbox_config);
            if (kmbox_controller_) {
                hardware_mouse_available_ = kmbox_controller_->Initialize();
                if (hardware_mouse_available_) {
                    std::cout << "[高级自瞄] 硬件鼠标控制器初始化成功" << std::endl;
                } else {
                    std::cout << "[高级自瞄] 硬件鼠标控制器初始化失败，将使用系统鼠标" << std::endl;
                }
            }
        }
        
        running_ = true;
        
        std::cout << "[高级自瞄] 初始化完成" << std::endl;
        std::cout << "[高级自瞄] PID参数: Kp=" << config_.pid_config.kp 
                  << ", Ki=" << config_.pid_config.ki << ", Kd=" << config_.pid_config.kd << std::endl;
        std::cout << "[高级自瞄] 自瞄圆环半径: " << config_.aim_circle_radius << std::endl;
        std::cout << "[高级自瞄] 硬件鼠标: " << (hardware_mouse_available_ ? "可用" : "不可用") << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[高级自瞄] 初始化异常: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[高级自瞄] 初始化发生未知异常" << std::endl;
        return false;
    }
}

void AdvancedAimingSystem::Shutdown() {
    if (!running_) return;
    
    std::cout << "[高级自瞄] 正在关闭..." << std::endl;
    
    running_ = false;
    enabled_ = false;
    
    // 关闭KMBox控制器
    if (kmbox_controller_) {
        kmbox_controller_->Disconnect();
        kmbox_controller_.reset();
    }
    
    // 重置PID控制器
    if (pid_controller_) {
        pid_controller_->Reset();
        pid_controller_.reset();
    }
    
    hardware_mouse_available_ = false;
    has_valid_target_ = false;
    
    std::cout << "[高级自瞄] 已关闭" << std::endl;
}

bool AdvancedAimingSystem::IsInitialized() const {
    return running_ && pid_controller_ != nullptr;
}

void AdvancedAimingSystem::UpdateConfig(const AdvancedAimingConfig& config) {
    config_ = config;
    
    if (pid_controller_) {
        pid_controller_->UpdateConfig(config_.pid_config);
    }
    
    if (kmbox_controller_) {
        kmbox_controller_->UpdateConfig(config_.kmbox_config);
    }
    
    std::cout << "[高级自瞄] 配置已更新" << std::endl;
}

AdvancedAimingConfig AdvancedAimingSystem::GetConfig() const {
    return config_;
}

void AdvancedAimingSystem::SetEnabled(bool enabled) {
    enabled_ = enabled;
    
    if (pid_controller_) {
        pid_controller_->SetEnabled(enabled);
    }
    
    if (enabled) {
        std::cout << "[高级自瞄] 已启用" << std::endl;
    } else {
        std::cout << "[高级自瞄] 已禁用" << std::endl;
        HandleTargetLost();  // 清除当前目标
    }
}

bool AdvancedAimingSystem::IsEnabled() const {
    return enabled_ && running_;
}

void AdvancedAimingSystem::ProcessDetections(const std::vector<Detection>& detections) {
    if (!IsEnabled() || !IsInitialized()) {
        return;
    }
    
    // 检查自瞄键是否按下
    if (!IsAimKeyPressed()) {
        HandleTargetLost();
        return;
    }
    
    // 选择最佳目标
    Detection best_target = SelectBestTarget(detections);
    
    if (best_target.classId >= 0) {
        // 找到有效目标
        UpdateCurrentTarget(best_target);
        
        // 计算瞄准点
        auto aim_point = CalculateAimPoint(best_target);
        
        // 使用PID控制器计算移动量
        auto pid_output = pid_controller_->Update(
            aim_point[0], aim_point[1], 
            static_cast<float>(config_.screen_center_x), 
            static_cast<float>(config_.screen_center_y)
        );
        
        // 执行鼠标移动
        bool move_success = ExecuteMouseMove(pid_output[0], pid_output[1]);
        
        // 更新统计信息
        UpdateStats(detections, move_success);
        
        // 调试输出
        if (config_.debug_output) {
            PrintTargetInfo(best_target);
            PrintPIDInfo(pid_controller_->GetDebugInfo());
        }
        
    } else {
        // 没有找到有效目标
        HandleTargetLost();
        UpdateStats(detections, false);
    }
}

bool AdvancedAimingSystem::IsAimKeyPressed() {
    // 检查鼠标右键 (0x02)
    return (GetAsyncKeyState(0x02) & 0x8000) != 0;
}

void AdvancedAimingSystem::ForceAimAt(float x, float y) {
    if (!IsEnabled() || !IsInitialized()) {
        return;
    }
    
    auto pid_output = pid_controller_->Update(
        x, y,
        static_cast<float>(config_.screen_center_x),
        static_cast<float>(config_.screen_center_y)
    );
    
    ExecuteMouseMove(pid_output[0], pid_output[1]);
}

AimingStats AdvancedAimingSystem::GetStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

void AdvancedAimingSystem::ResetStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = {};
    stats_.hardware_mouse_active = hardware_mouse_available_;
}

PIDController::DebugInfo AdvancedAimingSystem::GetPIDDebugInfo() const {
    if (pid_controller_) {
        return pid_controller_->GetDebugInfo();
    }
    return {};
}

void AdvancedAimingSystem::SetDebugOutput(bool enabled) {
    config_.debug_output = enabled;
}

Detection AdvancedAimingSystem::SelectBestTarget(const std::vector<Detection>& detections) {
    if (detections.empty()) {
        Detection invalid_target = {};
        invalid_target.classId = -1;
        return invalid_target;
    }
    
    std::vector<Detection> valid_targets;
    
    // 筛选在自瞄圆环内的目标
    for (const auto& det : detections) {
        if (IsTargetInAimCircle(det)) {
            valid_targets.push_back(det);
        }
    }
    
    if (valid_targets.empty()) {
        Detection invalid_target = {};
        invalid_target.classId = -1;
        return invalid_target;
    }
    
    // 按优先级排序
    std::sort(valid_targets.begin(), valid_targets.end(), 
              [this](const Detection& a, const Detection& b) {
                  return CalculateTargetPriority(a) > CalculateTargetPriority(b);
              });
    
    return valid_targets[0];
}

bool AdvancedAimingSystem::IsTargetInAimCircle(const Detection& detection) {
    float distance = CalculateTargetDistance(detection);
    return distance <= config_.aim_circle_radius;
}

float AdvancedAimingSystem::CalculateTargetDistance(const Detection& detection) {
    float center_x = detection.x + detection.width / 2.0f;
    float center_y = detection.y + detection.height / 2.0f;
    
    float dx = center_x - config_.screen_center_x;
    float dy = center_y - config_.screen_center_y;
    
    return std::sqrt(dx * dx + dy * dy);
}

float AdvancedAimingSystem::CalculateTargetPriority(const Detection& detection) {
    float priority = 0.0f;
    
    // 类别优先级
    if (detection.classId == config_.primary_class) {
        priority += 100.0f;
    } else if (detection.classId == config_.secondary_class) {
        priority += 50.0f;
    }
    
    // 距离优先级 (距离越近优先级越高)
    float distance = CalculateTargetDistance(detection);
    priority += (config_.aim_circle_radius - distance) / config_.aim_circle_radius * 50.0f;
    
    // 置信度优先级
    priority += detection.confidence * 20.0f;
    
    return priority;
}

void AdvancedAimingSystem::UpdateCurrentTarget(const Detection& target) {
    std::lock_guard<std::mutex> lock(target_mutex_);

    current_target_ = target;
    has_valid_target_ = true;
    target_lost_frames_ = 0;
    last_target_time_ = std::chrono::steady_clock::now();

    // 更新PID控制器的目标状态
    if (pid_controller_) {
        float center_x = target.x + target.width / 2.0f;
        float center_y = target.y + target.height / 2.0f;
        pid_controller_->UpdateTargetState(center_x, center_y);
    }
}

void AdvancedAimingSystem::HandleTargetLost() {
    std::lock_guard<std::mutex> lock(target_mutex_);

    target_lost_frames_++;

    if (target_lost_frames_ >= config_.target_lost_timeout) {
        has_valid_target_ = false;
        current_target_.classId = -1;

        if (pid_controller_) {
            pid_controller_->SetTargetLost();
        }
    }
}

bool AdvancedAimingSystem::ShouldSwitchTarget(const Detection& new_target) {
    if (!has_valid_target_) {
        return true;
    }

    float current_priority = CalculateTargetPriority(current_target_);
    float new_priority = CalculateTargetPriority(new_target);

    return new_priority > current_priority + config_.target_switch_threshold;
}

std::array<float, 2> AdvancedAimingSystem::CalculateAimPoint(const Detection& target) {
    // 计算目标中心点
    float center_x = target.x + target.width / 2.0f;
    float center_y = target.y + target.height / 2.0f;

    // 应用目标偏移
    auto offset = ApplyTargetOffset(target);

    return {center_x + offset[0], center_y + offset[1]};
}

std::array<float, 2> AdvancedAimingSystem::ApplyTargetOffset(const Detection& target) {
    float offset_x = 0.0f;
    float offset_y = 0.0f;

    if (target.classId == 0) {
        // 头部目标：向上偏移
        offset_y = -target.height * (0.5f - config_.head_offset_y);
    } else if (target.classId == 1) {
        // 身体目标：中心偏移
        offset_y = -target.height * (0.5f - config_.body_offset_y);
    }

    return {offset_x, offset_y};
}

bool AdvancedAimingSystem::ExecuteMouseMove(float dx, float dy) {
    if (std::abs(dx) < 0.1f && std::abs(dy) < 0.1f) {
        return true;  // 移动量太小，跳过
    }

    // 优先尝试硬件鼠标
    if (hardware_mouse_available_ && TryHardwareMouseMove(dx, dy)) {
        return true;
    }

    // 回退到系统鼠标
    return TrySystemMouseMove(dx, dy);
}

bool AdvancedAimingSystem::TryHardwareMouseMove(float dx, float dy) {
    if (!kmbox_controller_ || !kmbox_controller_->IsEnabled()) {
        return false;
    }

    int move_x = static_cast<int>(std::round(dx));
    int move_y = static_cast<int>(std::round(dy));

    return kmbox_controller_->MoveMouseAuto(move_x, move_y);
}

bool AdvancedAimingSystem::TrySystemMouseMove(float dx, float dy) {
    try {
        POINT current_pos;
        if (!GetCursorPos(&current_pos)) {
            return false;
        }

        int new_x = current_pos.x + static_cast<int>(std::round(dx));
        int new_y = current_pos.y + static_cast<int>(std::round(dy));

        return SetCursorPos(new_x, new_y) != 0;
    } catch (...) {
        return false;
    }
}

void AdvancedAimingSystem::UpdateStats(const std::vector<Detection>& detections, bool aim_executed) {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    stats_.total_detections += static_cast<int>(detections.size());

    if (has_valid_target_) {
        stats_.valid_targets++;
        stats_.current_target_type = GetTargetTypeName(current_target_.classId);

        if (aim_executed) {
            stats_.successful_aims++;
        }

        // 更新平均误差
        if (pid_controller_) {
            auto debug_info = pid_controller_->GetDebugInfo();
            float current_error = std::sqrt(debug_info.error_x * debug_info.error_x +
                                          debug_info.error_y * debug_info.error_y);
            stats_.average_error = (stats_.average_error * 0.9f) + (current_error * 0.1f);
        }
    } else {
        stats_.current_target_type = "无目标";
    }

    stats_.hardware_mouse_active = hardware_mouse_available_ &&
                                   kmbox_controller_ &&
                                   kmbox_controller_->IsEnabled();
}

void AdvancedAimingSystem::DebugPrint(const std::string& message) {
    if (config_.debug_output) {
        std::cout << "[高级自瞄调试] " << message << std::endl;
    }
}

void AdvancedAimingSystem::PrintTargetInfo(const Detection& target) {
    std::cout << "[目标信息] 类型=" << GetTargetTypeName(target.classId)
              << ", 位置=(" << target.x << "," << target.y << ")"
              << ", 尺寸=" << target.width << "x" << target.height
              << ", 置信度=" << target.confidence << std::endl;
}

void AdvancedAimingSystem::PrintPIDInfo(const PIDController::DebugInfo& info) {
    std::cout << "[PID信息] 误差=(" << info.error_x << "," << info.error_y << ")"
              << ", 输出=(" << info.output_x << "," << info.output_y << ")"
              << ", 距离=" << info.distance
              << ", Kp=" << info.adaptive_kp << std::endl;
}

std::string AdvancedAimingSystem::GetTargetTypeName(int class_id) {
    switch (class_id) {
        case 0: return "头部";
        case 1: return "身体";
        default: return "未知";
    }
}

float AdvancedAimingSystem::GetCurrentTime() {
    auto now = std::chrono::steady_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<float>(duration).count();
}
